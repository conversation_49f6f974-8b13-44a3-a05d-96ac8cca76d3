<template>
  <content-box
    class="go-content-backgrounds"
    :class="{ scoped: !chartLayoutStore.getBackgrounds }"
    title="背景"
    :depth="2"
    @back="backHandle"
  >
    <template #icon>
      <n-icon size="16" :depth="2" :component="ImageIcon" />
    </template>

    <div class="content">
      <!-- 集成统一图片管理组件，默认显示背景标签 -->
      <photos-manage-component :default-tag="ImageTagEnum.BACKGROUND" />
    </div>
  </content-box>
</template>

<script setup lang="ts">
import { ContentBox } from '../ContentBox/index'
import { useChartLayoutStore } from '@/store/modules/chartLayoutStore/chartLayoutStore'
import { ChartLayoutStoreEnum } from '@/store/modules/chartLayoutStore/chartLayoutStore.d'
import { icon } from '@/plugins'
import PhotosManageComponent from '@/packages/components/Photos/Manage/PhotosManage.vue'
import { ImageTagEnum } from '@/packages/components/Photos/index'

const { ImageIcon } = icon.ionicons5
const chartLayoutStore = useChartLayoutStore()

// 返回处理
const backHandle = () => {
  chartLayoutStore.setItem(ChartLayoutStoreEnum.BACKGROUNDS, false)
}
</script>

<style lang="scss" scoped>
$width: 280px;

.go-content-backgrounds {
  width: $width;
  overflow: hidden;
  transition: all 0.3s ease;

  &.scoped {
    width: 0;
  }

  .content {
    padding: 16px;
  }
}
</style>