import { http } from '@/api/http'
import { httpErrorHandle } from '@/utils'
import { ContentTypeEnum, RequestHttpEnum, ModuleTypeEnum } from '@/enums/httpEnum'
import { ProjectItem, ProjectDetail } from './project'

// * 项目列表
export const projectListApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.GET)<ProjectItem[]>(`${ModuleTypeEnum.PROJECT}/list`, data)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 新增项目
export const createProjectApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.POST)<{
      /**
       * 项目id
       */
      id: number
    }>(`${ModuleTypeEnum.PROJECT}/create`, data)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 获取项目
export const fetchProjectApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.GET)<ProjectDetail>(`${ModuleTypeEnum.PROJECT}/getData`, data)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 保存项目
export const saveProjectApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.POST)(
      `${ModuleTypeEnum.PROJECT}/save/data`,
      data,
      ContentTypeEnum.FORM_URLENCODED
    )
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 修改项目基础信息
export const updateProjectApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.POST)(`${ModuleTypeEnum.PROJECT}/edit`, data)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 删除项目
export const deleteProjectApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.DELETE)(`${ModuleTypeEnum.PROJECT}/delete`, data)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 修改发布状态 [-1未发布,1发布]
export const changeProjectReleaseApi = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.PUT)(`${ModuleTypeEnum.PROJECT}/publish`, data)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 上传文件
export const uploadFile = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.POST)<{
      /**
       * 文件地址
       */
      fileName: string,
      fileurl: string,
    }>(`${ModuleTypeEnum.PROJECT}/upload`, data, ContentTypeEnum.FORM_DATA)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 上传背景图片
export const uploadBackgroundImage = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.POST)<{
      /**
       * 背景ID
       */
      id: number,
      /**
       * 文件名
       */
      fileName: string,
      /**
       * 文件地址
       */
      fileurl: string,
    }>(`${ModuleTypeEnum.PROJECT}/uploadBackground`, data, ContentTypeEnum.FORM_DATA)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 获取项目背景列表
export const getProjectBackgrounds = async (projectId: string) => {
  try {
    const res = await http(RequestHttpEnum.GET)<{
      id: number,
      projectId: string,
      fileName: string,
      fileUrl: string,
      fileSize: number,
      isActive: boolean,
      createTime: string,
      updateTime: string,
      createUserId: string,
    }[]>(`${ModuleTypeEnum.PROJECT}/backgrounds?projectId=${projectId}`)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 删除项目背景
export const deleteProjectBackground = async (projectId: string, backgroundId: number) => {
  try {
    const res = await http(RequestHttpEnum.DELETE)(`${ModuleTypeEnum.PROJECT}/background/delete?projectId=${projectId}&backgroundId=${backgroundId}`)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 设置活跃背景
export const setActiveBackground = async (projectId: string, backgroundId: number) => {
  try {
    const res = await http(RequestHttpEnum.POST)(`${ModuleTypeEnum.PROJECT}/background/setActive?projectId=${projectId}&backgroundId=${backgroundId}`)
    return res
  } catch {
    httpErrorHandle()
  }
}

// ==================== 新的统一图片管理API ====================

// * 上传图片
export const uploadImage = async (data: object) => {
  try {
    const res = await http(RequestHttpEnum.POST)<{
      /**
       * 图片ID
       */
      id: number,
      /**
       * 文件名
       */
      fileName: string,
      /**
       * 文件地址
       */
      fileurl: string,
      /**
       * 标签数组
       */
      tags: string[],
    }>(`${ModuleTypeEnum.PROJECT}/images/upload`, data, ContentTypeEnum.FORM_DATA)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 获取项目图片列表
export const getProjectImages = async (projectId: string, tags?: string[]) => {
  try {
    const tagsParam = tags && tags.length > 0 ? `&tags=${tags.join(',')}` : ''
    const res = await http(RequestHttpEnum.GET)<{
      id: number,
      projectId: string,
      fileName: string,
      fileUrl: string,
      fileSize: number,
      tags: string[],
      isActive: boolean,
      createTime: string,
      updateTime: string,
      createUserId: string,
    }[]>(`${ModuleTypeEnum.PROJECT}/images?projectId=${projectId}${tagsParam}`)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 删除项目图片
export const deleteProjectImage = async (projectId: string, imageId: number) => {
  try {
    const res = await http(RequestHttpEnum.DELETE)(`${ModuleTypeEnum.PROJECT}/images/${imageId}/delete?projectId=${projectId}`)
    return res
  } catch {
    httpErrorHandle()
  }
}

// * 设置活跃图片
export const setActiveImage = async (projectId: string, imageId: number) => {
  try {
    const res = await http(RequestHttpEnum.POST)(`${ModuleTypeEnum.PROJECT}/images/${imageId}/setActive?projectId=${projectId}`)
    return res
  } catch {
    httpErrorHandle()
  }
}
