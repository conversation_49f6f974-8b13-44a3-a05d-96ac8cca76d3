import { ChartFrameEnum, ConfigType, PackagesCategoryEnum } from '@/packages/index.d'
import { ImageConfig } from '@/packages/components/Informations/Mores/Image/index'
import { BackgroundCategoryEnum, BackgroundCategoryEnumName } from '../index.d'
import { setLocalStorage, getLocalStorage } from '@/utils'
import { StorageEnum } from '@/enums/storageEnum'
import { FileTypeEnum } from '@/enums/fileTypeEnum'
import { backgroundImageSize } from '@/settings/designSetting'
import { usePackagesStore } from '@/store/modules/packagesStore/packagesStore'
import { uploadBackgroundImage, getProjectBackgrounds } from '@/api/path'
import { ResultEnum } from '@/enums/httpEnum'
import { fetchRouteParamsLocation } from '@/utils'

const StoreKey = StorageEnum.GO_USER_MEDIA_BACKGROUNDS

/**
 * 上传完成事件类型
 */
type UploadCompletedEventType = {
  fileName: string
  url: string
}

// 背景列表缓存
let backgroundsCache: ConfigType[] = []
let backgroundsPromise: Promise<ConfigType[]> | null = null

/**
 * 获取完整的图片URL
 */
const getFullImageUrl = (fileUrl: string): string => {
  // 如果已经是完整URL，直接返回
  if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
    return fileUrl
  }

  // 获取当前前端域名和端口
  const { protocol, hostname, port } = window.location
  const baseUrl = `${protocol}//${hostname}${port ? ':' + port : ''}`

  // 拼接完整URL，确保fileUrl以/开头
  const normalizedFileUrl = fileUrl.startsWith('/') ? fileUrl : `/${fileUrl}`
  return `${baseUrl}${normalizedFileUrl}`
}

/**
 * 从后端获取背景列表
 */
const fetchBackgroundsFromServer = async (): Promise<ConfigType[]> => {
  try {
    const projectId = fetchRouteParamsLocation()
    const res = await getProjectBackgrounds(projectId)

    if (res && res.code === ResultEnum.SUCCESS) {
      const serverBackgrounds = res.data || []

      // 将服务器背景数据转换为组件配置格式
      const userBackgroundsList = serverBackgrounds.map((bg: any) => {
        const fullImageUrl = getFullImageUrl(bg.fileUrl)
        return {
          ...ImageConfig,
          category: BackgroundCategoryEnum.MANAGE,
          categoryName: BackgroundCategoryEnumName.MANAGE,
          package: PackagesCategoryEnum.BACKGROUNDS,
          chartFrame: ChartFrameEnum.STATIC,
          title: bg.fileName,
          image: fullImageUrl,
          dataset: fullImageUrl,
          id: bg.id,
          redirectComponent: `${ImageConfig.package}/${ImageConfig.category}/${ImageConfig.key}`
        }
      })

      // 更新本地存储
      setLocalStorage(StoreKey, userBackgroundsList)

      return userBackgroundsList
    }
  } catch (error) {
    console.error('获取背景列表失败:', error)
  }

  // 如果获取失败，返回本地存储的数据
  return getLocalStorage(StoreKey) || []
}

/**
 * 获取背景列表（带缓存）
 */
const getBackgroundsList = async (): Promise<ConfigType[]> => {
  if (backgroundsPromise) {
    return backgroundsPromise
  }

  backgroundsPromise = fetchBackgroundsFromServer()
  backgroundsCache = await backgroundsPromise
  backgroundsPromise = null

  return backgroundsCache
}

/**
 * 刷新背景列表
 */
const refreshBackgroundsList = async (): Promise<ConfigType[]> => {
  backgroundsPromise = null
  backgroundsCache = []
  return await getBackgroundsList()
}

const uploadFile = (callback: Function | null = null) => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.png,.jpg,.jpeg,.gif' // 这里只允许部分图片类型
  input.onchange = async () => {
    if (!input.files || !input.files.length) return
    const file = input.files[0]
    const { name, size, type } = file
    if (size > 1024 * 1024 * backgroundImageSize) {
      window['$message'].warning(`背景图片超出 ${backgroundImageSize}M 限制，请重新上传！`)
      return false
    }
    if (type !== FileTypeEnum.PNG && type !== FileTypeEnum.JPEG && type !== FileTypeEnum.GIF) {
      window['$message'].warning('文件格式不符合，请重新上传！')
      return false
    }

    try {
      // 显示上传中提示
      window['$message'].loading('正在上传背景图片...', { duration: 0 })

      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', file)
      formData.append('projectId', fetchRouteParamsLocation())

      // 调用后端上传接口
      const uploadRes = await uploadBackgroundImage(formData)

      // 清除loading提示
      window['$message'].destroyAll()

      if (uploadRes && uploadRes.code === ResultEnum.SUCCESS) {
        window['$message'].success('背景图片上传成功！')

        // 刷新背景列表
        await refreshBackgroundsList()

        // 通知组件更新
        callback && callback({
          id: uploadRes.data.id,
          fileName: uploadRes.data.fileName,
          url: uploadRes.data.fileurl,
          needRefresh: true // 标记需要刷新
        })
      } else {
        window['$message'].error('背景图片上传失败，请重试！')
      }
    } catch (error) {
      // 清除loading提示
      window['$message'].destroyAll()
      window['$message'].error('背景图片上传失败：' + (error as Error).message)
    }
  }
  input.click()
}

// 上传卡片配置
const uploadConfig = {
  ...ImageConfig,
  category: BackgroundCategoryEnum.MANAGE,
  categoryName: BackgroundCategoryEnumName.MANAGE,
  package: PackagesCategoryEnum.BACKGROUNDS,
  chartFrame: ChartFrameEnum.STATIC,
  title: '点击上传背景图片',
  image: 'upload.png',
  redirectComponent: `${ImageConfig.package}/${ImageConfig.category}/${ImageConfig.key}`,
  disabled: true,
  configEvents: {
    // 点击上传事件
    addHandle: (backgroundConfig: ConfigType) => {
      uploadFile(async (e: any) => {
        if (e.needRefresh) {
          // 上传成功，刷新背景列表
          const packagesStore = usePackagesStore()
          await packagesStore.refreshBackgrounds()
        }
      })
    }
  }
}

// 预设背景图片列表
const presetBackgrounds = [
  { imageName: 'background1', imageUrl: 'https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel1.jpeg' },
  { imageName: 'background2', imageUrl: 'https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel2.jpeg' }
]

const presetBackgroundConfigs = presetBackgrounds.map(i => ({
  ...ImageConfig,
  category: BackgroundCategoryEnum.MANAGE,
  categoryName: BackgroundCategoryEnumName.MANAGE,
  package: PackagesCategoryEnum.BACKGROUNDS,
  chartFrame: ChartFrameEnum.STATIC,
  image: i.imageUrl,
  dataset: i.imageUrl,
  title: i.imageName,
  redirectComponent: `${ImageConfig.package}/${ImageConfig.category}/${ImageConfig.key}`
}))

/**
 * 获取完整的背景配置列表
 */
const getBackgroundConfigs = async (): Promise<ConfigType[]> => {
  const userBackgrounds = await getBackgroundsList()
  return [uploadConfig, ...userBackgrounds, ...presetBackgroundConfigs]
}

// 导出异步函数而不是静态数组
export default getBackgroundConfigs

// 同时导出同步版本用于兼容性（使用缓存数据）
export const getBackgroundConfigsSync = (): ConfigType[] => {
  const userBackgrounds = getLocalStorage(StoreKey) || []
  return [uploadConfig, ...userBackgrounds, ...presetBackgroundConfigs]
}
