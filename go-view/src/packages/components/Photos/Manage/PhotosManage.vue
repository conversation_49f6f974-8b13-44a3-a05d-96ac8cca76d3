<template>
  <div class="photos-manage">
    <!-- 标签切换 -->
    <div class="tag-tabs">
      <div 
        v-for="(config, tag) in ImageTagConfig" 
        :key="tag"
        class="tag-tab"
        :class="{ active: state.currentTag === tag }"
        :style="{ borderColor: state.currentTag === tag ? config.color : '#d9d9d9' }"
        @click="switchTag(tag as ImageTagEnum)"
      >
        <span :style="{ color: state.currentTag === tag ? config.color : '#666' }">
          {{ config.label }}
        </span>
      </div>
    </div>

    <!-- 上传区域 -->
    <div class="upload-area" v-if="state.currentTag !== ImageTagEnum.ALL">
      <n-upload
        :show-file-list="false"
        :custom-request="handleUpload"
        accept="image/*"
        :disabled="state.loading"
      >
        <n-upload-dragger>
          <div class="upload-content">
            <n-icon size="48" :depth="3">
              <CloudUploadOutline />
            </n-icon>
            <n-text style="font-size: 16px">
              点击上传{{ ImageTagConfig[state.currentTag]?.label }}图片
            </n-text>
            <n-p depth="3" style="margin: 8px 0 0 0">
              支持 PNG、JPG、JPEG、GIF、WebP、SVG 格式，大小不超过 10MB
            </n-p>
          </div>
        </n-upload-dragger>
      </n-upload>
      
      <!-- 上传进度 -->
      <n-progress 
        v-if="state.uploadProgress > 0"
        type="line" 
        :percentage="state.uploadProgress"
        :show-indicator="false"
        style="margin-top: 10px"
      />
    </div>

    <!-- 图片列表 -->
    <div class="images-grid" v-loading="state.loading">
      <div 
        v-for="image in getFilteredImages()" 
        :key="image.id"
        class="image-item"
        :class="{ active: image.isActive }"
        @click="handleImageClick(image)"
      >
        <div class="image-wrapper">
          <img 
            :src="image.image" 
            :alt="image.title"
            class="image-preview"
            @error="handleImageError"
          />
          
          <!-- 活跃状态指示器 -->
          <div v-if="image.isActive" class="active-indicator">
            <n-icon size="16" color="white">
              <CheckmarkCircle />
            </n-icon>
          </div>
          
          <!-- 操作按钮 -->
          <div class="image-actions" v-if="!image.id.toString().startsWith('preset_')">
            <n-button 
              size="small" 
              type="primary" 
              ghost
              @click.stop="setActiveImageItem(Number(image.id))"
              v-if="state.currentTag === ImageTagEnum.BACKGROUND"
            >
              设为背景
            </n-button>
            <n-button 
              size="small" 
              type="error" 
              ghost
              @click.stop="handleDelete(Number(image.id))"
            >
              删除
            </n-button>
          </div>
        </div>
        
        <div class="image-info">
          <n-ellipsis style="max-width: 100%">
            {{ image.title }}
          </n-ellipsis>
          <div class="image-meta" v-if="image.fileSize">
            <span class="file-size">{{ formatFileSize(image.fileSize) }}</span>
            <span class="create-time" v-if="image.createTime">
              {{ formatTime(image.createTime) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!state.loading && getFilteredImages().length === 0" class="empty-state">
        <n-empty description="暂无图片">
          <template #extra>
            <n-button size="small" @click="loadImages()">
              刷新
            </n-button>
          </template>
        </n-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  NUpload,
  NUploadDragger,
  NIcon,
  NText,
  NP,
  NProgress,
  NButton,
  NEllipsis,
  NEmpty,
  useDialog
} from 'naive-ui'
import { CloudUploadOutline, CheckmarkCircle } from '@vicons/ionicons5'
import PhotosManageLogic from './index'
import { ConfigType } from '@/packages/index.d'
import { ImageTagEnum } from '../index'

// Props
const props = defineProps({
  defaultTag: {
    type: String,
    default: 'all'
  }
})

// 使用逻辑
const {
  state,
  ImageTagEnum,
  ImageTagConfig,
  switchTag,
  uploadImageFile,
  deleteImage,
  setActiveImageItem,
  getFilteredImages,
  loadImages
} = PhotosManageLogic

const dialog = useDialog()

// 处理上传
const handleUpload = async ({ file }: { file: { file: File } }) => {
  const tags = state.currentTag === ImageTagEnum.ALL ? [ImageTagEnum.COMPONENT] : [state.currentTag]
  await uploadImageFile(file.file, tags)
}

// 处理图片点击
const handleImageClick = (image: ConfigType) => {
  // 这里可以添加图片选择逻辑
  console.log('选择图片:', image)
}

// 处理删除
const handleDelete = (imageId: number) => {
  dialog.warning({
    title: '确认删除',
    content: '确定要删除这张图片吗？删除后无法恢复。',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      deleteImage(imageId)
    }
  })
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 格式化时间
const formatTime = (timeStr: string): string => {
  const date = new Date(timeStr)
  return date.toLocaleDateString()
}

// 初始化
onMounted(() => {
  // 设置默认标签
  if (props.defaultTag !== 'all') {
    switchTag(props.defaultTag as ImageTagEnum)
  }
})
</script>

<style scoped lang="scss">
.photos-manage {
  padding: 16px;
  
  .tag-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    
    .tag-tab {
      padding: 8px 16px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.active {
        background-color: rgba(64, 158, 255, 0.1);
      }
    }
  }
  
  .upload-area {
    margin-bottom: 16px;
    
    .upload-content {
      text-align: center;
      padding: 20px;
    }
  }
  
  .images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    
    .image-item {
      border: 2px solid transparent;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.active {
        border-color: #67c23a;
      }
      
      .image-wrapper {
        position: relative;
        width: 100%;
        height: 80px;
        
        .image-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;
          background-color: #f5f5f5;
        }
        
        .active-indicator {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 20px;
          height: 20px;
          background-color: #67c23a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .image-actions {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
          padding: 8px 4px 4px;
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.3s;
        }
        
        &:hover .image-actions {
          opacity: 1;
        }
      }
      
      .image-info {
        padding: 8px;
        background-color: white;
        
        .image-meta {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
    }
    
    .empty-state {
      grid-column: 1 / -1;
      text-align: center;
      padding: 40px 20px;
    }
  }
}
</style>
