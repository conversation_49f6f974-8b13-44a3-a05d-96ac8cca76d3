import { ref, reactive, onMounted, nextTick } from 'vue'
import { icon } from '@/plugins'
import { ConfigType } from '@/packages/index.d'
import { uploadImage, getProjectImages, deleteProjectImage, setActiveImage } from '@/api/path/project.api'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { fetchRouteParamsLocation } from '@/utils'
import { ImageTagEnum, ImageTagConfig } from '../index'
import { ResultEnum } from '@/enums/httpEnum'
// 获取全局挂载的 $message 的函数
const getMessage = () => window['$message']

const chartEditStore = useChartEditStore()

// 图片数据接口
interface ImageItem {
  id: number
  projectId: string
  fileName: string
  fileUrl: string
  fileSize: number
  tags: string[]
  isActive: boolean
  createTime: string
  updateTime: string
  createUserId: string
}

// 组件状态
const state = reactive({
  currentTag: ImageTagEnum.ALL as ImageTagEnum,
  images: [] as ImageItem[],
  loading: false,
  uploadProgress: 0,
  showUploadModal: false
})

// 预设图片（保持兼容性）
const presetImages: ConfigType[] = [
  {
    id: 'preset_1',
    title: 'carousel1',
    image: 'https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel1.jpeg'
  },
  {
    id: 'preset_2', 
    title: 'carousel2',
    image: 'https://naive-ui.oss-cn-beijing.aliyuncs.com/carousel-img/carousel2.jpeg'
  }
]

/**
 * 获取完整的图片URL
 */
const getFullImageUrl = (fileUrl: string): string => {
  // 如果已经是完整URL，直接返回
  if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
    return fileUrl
  }

  // 获取当前前端域名和端口
  const { protocol, hostname, port } = window.location
  const baseUrl = `${protocol}//${hostname}${port ? ':' + port : ''}`

  // 拼接完整URL，确保fileUrl以/开头
  const normalizedFileUrl = fileUrl.startsWith('/') ? fileUrl : `/${fileUrl}`
  return `${baseUrl}${normalizedFileUrl}`
}

/**
 * 加载项目图片列表
 */
const loadImages = async (tag?: ImageTagEnum) => {
  try {
    state.loading = true
    const currentTag = tag || state.currentTag
    
    // 根据标签过滤
    const tags = currentTag === ImageTagEnum.ALL ? [] : [currentTag]
    
    const projectId = fetchRouteParamsLocation()
    const res = await getProjectImages(projectId, tags)
    
    if (res && res.code === ResultEnum.SUCCESS) {
      state.images = res.data || []
    } else {
      getMessage()?.error('获取图片列表失败')
      state.images = []
    }
  } catch (error) {
    console.error('加载图片列表失败:', error)
    getMessage()?.error('加载图片列表失败')
    state.images = []
  } finally {
    state.loading = false
  }
}

/**
 * 切换标签
 */
const switchTag = async (tag: ImageTagEnum) => {
  state.currentTag = tag
  await loadImages(tag)
}

/**
 * 上传图片
 */
const uploadImageFile = async (file: File, tags: string[] = [ImageTagEnum.COMPONENT]) => {
  try {
    state.uploadProgress = 0
    
    // 文件大小检查
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      getMessage()?.error('文件大小不能超过10MB')
      return false
    }

    // 文件类型检查
    const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      getMessage()?.error('不支持的文件格式，请上传 PNG、JPG、JPEG、GIF、WebP 或 SVG 格式的图片')
      return false
    }
    
    // 构建上传数据
    const formData = new FormData()
    formData.append('file', file)
    formData.append('projectId', fetchRouteParamsLocation())
    formData.append('tags', tags.join(','))
    
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (state.uploadProgress < 90) {
        state.uploadProgress += 10
      }
    }, 100)
    
    const res = await uploadImage(formData)
    
    clearInterval(progressInterval)
    state.uploadProgress = 100
    
    if (res && res.code === ResultEnum.SUCCESS) {
      getMessage()?.success('图片上传成功')
      // 重新加载图片列表
      await loadImages()
      return true
    } else {
      getMessage()?.error(res?.msg || '图片上传失败')
      return false
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    getMessage()?.error('上传图片失败')
    return false
  } finally {
    state.uploadProgress = 0
  }
}

/**
 * 删除图片
 */
const deleteImage = async (imageId: number) => {
  try {
    const res = await deleteProjectImage(fetchRouteParamsLocation(), imageId)
    
    if (res && res.code === ResultEnum.SUCCESS) {
      getMessage()?.success('删除成功')
      // 重新加载图片列表
      await loadImages()
      return true
    } else {
      getMessage()?.error(res?.msg || '删除失败')
      return false
    }
  } catch (error) {
    console.error('删除图片失败:', error)
    getMessage()?.error('删除图片失败')
    return false
  }
}

/**
 * 设置活跃图片（主要用于背景）
 */
const setActiveImageItem = async (imageId: number) => {
  try {
    const res = await setActiveImage(fetchRouteParamsLocation(), imageId)
    
    if (res && res.code === ResultEnum.SUCCESS) {
      getMessage()?.success('设置成功')
      // 重新加载图片列表
      await loadImages()
      return true
    } else {
      getMessage()?.error(res?.msg || '设置失败')
      return false
    }
  } catch (error) {
    console.error('设置活跃图片失败:', error)
    getMessage()?.error('设置活跃图片失败')
    return false
  }
}

/**
 * 获取过滤后的图片列表（包含预设图片）
 */
const getFilteredImages = (): ConfigType[] => {
  const result: ConfigType[] = []
  
  // 添加预设图片（仅在"所有"或"背景"标签时显示）
  if (state.currentTag === ImageTagEnum.ALL || state.currentTag === ImageTagEnum.BACKGROUND) {
    result.push(...presetImages)
  }
  
  // 添加用户上传的图片
  state.images.forEach(image => {
    result.push({
      id: image.id.toString(),
      title: image.fileName,
      image: getFullImageUrl(image.fileUrl),
      tags: image.tags,
      isActive: image.isActive,
      fileSize: image.fileSize,
      createTime: image.createTime
    })
  })
  
  return result
}

/**
 * 初始化
 */
const init = async () => {
  await loadImages()
}

// 组件挂载时初始化
onMounted(() => {
  init()
})

// 导出状态和方法
export default {
  // 状态
  state,
  ImageTagEnum,
  ImageTagConfig,
  
  // 方法
  loadImages,
  switchTag,
  uploadImageFile,
  deleteImage,
  setActiveImageItem,
  getFilteredImages,
  getFullImageUrl,
  init,
  
  // 预设图片
  presetImages
}
