import Private from './Private'
import Share from './Share'
import Manage from './Manage'

// 图片标签枚举
export enum ImageTagEnum {
  ALL = 'all',
  ICON = 'icon',
  BACKGROUND = 'background',
  COMPONENT = 'component'
}

// 图片标签配置
export const ImageTagConfig = {
  [ImageTagEnum.ALL]: { label: '所有', color: '#409eff' },
  [ImageTagEnum.ICON]: { label: '图标', color: '#67c23a' },
  [ImageTagEnum.BACKGROUND]: { label: '背景', color: '#e6a23c' },
  [ImageTagEnum.COMPONENT]: { label: '组件', color: '#f56c6c' }
}

// 导出图片列表（保持兼容性）
export const PhotoList = [...Private, ...Share]

// 导出新的统一图片管理组件
export { Manage as PhotosManage }
