<template>
  <div class="ranking-list-container" :style="containerStyles">
    <!-- 标题 -->
    <div v-if="titleConfig.show" class="ranking-title" :style="titleStyles">
      {{ titleConfig.text }}
    </div>
    
    <!-- 表头 -->
    <div v-if="headerConfig.show" class="ranking-header" :style="headerStyles">
      <div class="header-rank" :style="getHeaderRankStyle()">排名</div>
      <div
        v-for="(column, index) in dataColumns"
        :key="column"
        class="header-column"
        :style="getHeaderColumnStyle(index)"
      >
        {{ getColumnDisplayName(column) }}
      </div>
    </div>
    
    <!-- 排行榜列表 -->
    <div class="ranking-list">
      <div 
        v-for="(item, index) in displayData" 
        :key="index"
        class="ranking-item"
        :style="getRowStyles(index)"
        @mouseenter="onRowHover(index, true)"
        @mouseleave="onRowHover(index, false)"
      >
        <!-- 排名 -->
        <div class="rank-number" :style="getRankStyles(index + 1)">
          <span v-if="(index + 1) <= 3" class="rank-medal">{{ getRankMedal(index + 1) }}</span>
          <span v-else>{{ index + 1 }}</span>
        </div>
        
        <!-- 动态数据列 -->
        <div
          v-for="(column, colIndex) in dataColumns"
          :key="column"
          class="data-column"
          :style="getDataColumnStyle(colIndex)"
        >
          {{ formatColumnValue(item[column], column) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, PropType, watch, onMounted, toRefs } from 'vue'
import { CreateComponentType } from '@/packages/index.d'
import { useChartDataFetch } from '@/hooks'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'

const props = defineProps({
  themeSetting: {
    type: Object,
    required: true
  },
  themeColor: {
    type: Object,
    required: true
  },
  chartConfig: {
    type: Object as PropType<CreateComponentType>,
    required: true
  }
})

const hoveredIndex = ref(-1)

// 获取组件宽高属性
const { w, h } = toRefs(props.chartConfig.attr)

// 配置项
const rankingStyle = computed(() => props.chartConfig.option.rankingStyle)
const containerConfig = computed(() => rankingStyle.value.containerStyle)
const titleConfig = computed(() => rankingStyle.value.titleStyle)
const headerConfig = computed(() => rankingStyle.value.headerStyle)
const rowConfig = computed(() => rankingStyle.value.rowStyle)
const rankConfig = computed(() => {
  const config = rankingStyle.value.rankStyle
  console.log('🔍 [DEBUG] rankConfig:', config)
  return config
})

const nameConfig = computed(() => {
  const config = rankingStyle.value.nameStyle
  console.log('🔍 [DEBUG] nameConfig:', config)
  return config
})

const valueConfig = computed(() => {
  const config = rankingStyle.value.valueStyle
  console.log('🔍 [DEBUG] valueConfig:', config)
  return config
})
const dataConfig = computed(() => props.chartConfig.option.dataConfig)

// 数据处理
const displayData = computed(() => {
  const data = props.chartConfig.option.dataset?.source || []
  const maxItems = dataConfig.value.maxItems || 10
  return data.slice(0, maxItems)
})

// 动态列计算
const dataColumns = computed(() => {
  // 优先使用 dimensions 字段定义的列
  const dimensions = props.chartConfig.option.dataset?.dimensions
  if (dimensions && dimensions.length > 0) {
    console.log('🔍 [DEBUG] dataColumns from dimensions:', dimensions)
    return dimensions
  }

  // 如果没有 dimensions，则从数据中推断所有字段
  const data = displayData.value
  if (data.length === 0) {
    console.log('🔍 [DEBUG] dataColumns: no data, returning empty array')
    return []
  }

  const firstItem = data[0]
  const columns = Object.keys(firstItem)
  console.log('🔍 [DEBUG] dataColumns from data keys:', columns)
  return columns
})

// 动态宽度计算
const layoutConfig = computed(() => {
  const dataColumnCount = dataColumns.value.length
  const totalColumnCount = dataColumnCount + 1 // 数据列 + 排名列
  const columnGap = 16 // 列间距
  // 总间距：数据列的左边距（排名列没有左边距）
  const totalGaps = columnGap * dataColumnCount

  // 计算可用宽度（容器宽度 - 内边距 - 间距）
  const containerPadding = containerConfig.value.padding * 2 // 左右内边距
  const availableWidthForColumns = w.value - containerPadding - totalGaps

  // 每列宽度 = (可用宽度 - 间距) / 总列数
  const columnWidth = totalColumnCount > 0
    ? `${Math.floor(availableWidthForColumns / totalColumnCount)}px`
    : '0px'

  const config = {
    columnWidth, // 所有列使用相同宽度
    columnGap,
    dataColumnCount,
    totalColumnCount,
    availableWidthForColumns,
    totalWidth: availableWidthForColumns + totalGaps + containerPadding
  }

  console.log('🔍 [DEBUG] layoutConfig:', config)
  return config
})

// 数据callback处理（预览时触发）
useChartDataFetch(props.chartConfig, useChartEditStore, (resData: any[]) => {
  props.chartConfig.option.dataset = resData
})

// 样式计算
const containerStyles = computed(() => ({
  backgroundColor: containerConfig.value.backgroundColor,
  borderRadius: `${containerConfig.value.borderRadius}px`,
  padding: `${containerConfig.value.padding}px`,
  border: containerConfig.value.border,
  height: `${h.value}px`,
  width: `${w.value}px`,
  boxSizing: 'border-box',
  overflow: containerConfig.value.showScrollbar ? 'auto' : 'hidden',
  display: 'flex',
  flexDirection: 'column'
}))

const titleStyles = computed(() => ({
  fontSize: `${titleConfig.value.fontSize}px`,
  fontWeight: titleConfig.value.fontWeight,
  color: titleConfig.value.color,
  textAlign: titleConfig.value.textAlign,
  marginBottom: `${titleConfig.value.marginBottom}px`
}))

const headerStyles = computed(() => ({
  backgroundColor: headerConfig.value.backgroundColor,
  color: headerConfig.value.color,
  fontSize: `${headerConfig.value.fontSize}px`,
  fontWeight: headerConfig.value.fontWeight,
  height: `${headerConfig.value.height}px`,
  borderRadius: `${headerConfig.value.borderRadius}px`,
  display: 'flex',
  alignItems: 'center',
  padding: '0', // 完全移除所有内边距
  marginBottom: '12px'
}))

const getRowStyles = (index: number) => ({
  height: `${rowConfig.value.height}px`,
  backgroundColor: hoveredIndex.value === index
    ? rowConfig.value.hoverBackgroundColor
    : rowConfig.value.backgroundColor,
  borderRadius: `${rowConfig.value.borderRadius}px`,
  marginBottom: `${rowConfig.value.marginBottom}px`,
  padding: '0', // 完全移除所有内边距
  display: 'flex',
  alignItems: 'center',
  transition: 'all 0.3s ease',
  cursor: 'pointer'
})

const getRankStyles = (rank: number) => ({
  width: layoutConfig.value.columnWidth,
  fontSize: `${rankConfig.value.fontSize}px`,
  fontWeight: rankConfig.value.fontWeight,
  color: rankConfig.value.topColors[rank] || rankConfig.value.color,
  textAlign: 'center',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  flexShrink: 1, // 允许收缩
  minWidth: '40px' // 设置最小宽度
})

const nameStyles = computed(() => {
  const styles = {
    fontSize: `${nameConfig.value.fontSize}px`,
    fontWeight: nameConfig.value.fontWeight,
    color: nameConfig.value.color,
    marginLeft: `${nameConfig.value.marginLeft}px`
  }
  console.log('🔍 [DEBUG] nameStyles computed:', styles)
  return styles
})

const valueStyles = computed(() => {
  const styles = {
    fontSize: `${valueConfig.value.fontSize}px`,
    fontWeight: valueConfig.value.fontWeight,
    color: valueConfig.value.color,
    textAlign: valueConfig.value.textAlign
  }
  console.log('🔍 [DEBUG] valueStyles computed:', styles)
  return styles
})

// 方法
const onRowHover = (index: number, isHover: boolean) => {
  hoveredIndex.value = isHover ? index : -1
}

const getRankMedal = (rank: number) => {
  const medals = { 1: '🥇', 2: '🥈', 3: '🥉' }
  return medals[rank] || rank
}

const formatValue = (value: number) => {
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  }
  return value.toString()
}

// 简化的方法实现
const getColumnDisplayName = (column: string) => {
  // 列名映射
  const columnNameMap = {
    '应用名称': '应用名称',
    '产品数量': '产品数量',
    'name': '名称',
    'value': '数值',
    'count': '数量'
  }
  return columnNameMap[column] || column
}

// 表头排名列样式计算
const getHeaderRankStyle = () => {
  const styles = {
    width: layoutConfig.value.columnWidth,
    textAlign: 'center',
    flexShrink: 0
  }
  console.log('🔍 [DEBUG] getHeaderRankStyle =', styles)
  return styles
}

// 表头列样式计算
const getHeaderColumnStyle = (index: number) => {
  const isLastColumn = index === dataColumns.value.length - 1
  const styles = {
    width: layoutConfig.value.columnWidth,
    marginLeft: `${layoutConfig.value.columnGap}px`,
    marginRight: '0px', // 完全移除右边距，包括最后一列
    textAlign: index === 0 ? 'left' : 'right',
    flexShrink: 1, // 允许收缩
    minWidth: index === 0 ? '60px' : '40px' // 设置最小宽度
  }
  console.log(`🔍 [DEBUG] getHeaderColumnStyle(${index}) =`, styles)
  return styles
}

// 数据列样式计算
const getDataColumnStyle = (index: number) => {
  const isFirstColumn = index === 0
  const isLastColumn = index === dataColumns.value.length - 1
  const styles = {
    width: layoutConfig.value.columnWidth,
    marginLeft: `${layoutConfig.value.columnGap}px`,
    marginRight: '0px', // 完全移除右边距，包括最后一列
    textAlign: isFirstColumn ? 'left' : 'right',
    flexShrink: 1, // 允许收缩
    minWidth: isFirstColumn ? '60px' : '40px', // 设置最小宽度，名称列稍宽
    // 第一列使用名称样式，其他列使用数值样式
    fontSize: isFirstColumn ? `${nameConfig.value.fontSize}px` : `${valueConfig.value.fontSize}px`,
    fontWeight: isFirstColumn ? nameConfig.value.fontWeight : valueConfig.value.fontWeight,
    color: isFirstColumn ? nameConfig.value.color : valueConfig.value.color
  }
  console.log(`🔍 [DEBUG] getDataColumnStyle(${index}) =`, styles)
  return styles
}

const formatColumnValue = (value: any, column: string) => {
  // 根据列类型格式化数值
  if (typeof value === 'number' && value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  }
  return value?.toString() || ''
}

// 组件挂载时的调试信息
onMounted(() => {
  console.log('🔍 [DEBUG] RankingList mounted')
  console.log('🔍 [DEBUG] props.chartConfig:', props.chartConfig)
  console.log('🔍 [DEBUG] displayData:', displayData.value)
  console.log('🔍 [DEBUG] dataColumns:', dataColumns.value)
})
</script>

<style scoped>
.ranking-list-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  position: relative;
}

/* 滚动条样式 */
.ranking-list-container::-webkit-scrollbar {
  width: 6px;
}

.ranking-list-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.ranking-list-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.ranking-list-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.ranking-list {
  margin: 0;
  padding: 0;
}

.ranking-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.header-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.header-column {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.header-column:first-child {
  text-align: left !important;
}

.header-column:last-child {
  text-align: right !important;
}

.ranking-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.rank-number {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.data-column {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.data-column:first-child {
  text-align: left !important;
}

.data-column:last-child {
  text-align: right !important;
}

.rank-medal {
  font-size: 20px;
}
</style>
