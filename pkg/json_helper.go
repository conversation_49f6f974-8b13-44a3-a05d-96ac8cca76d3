package pkg

import (
	"ccapi/pkg/types"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/beego/beego/v2/server/web/context"
)

// Unmarshal 解析 JSON 并为未传递的字段设置默认值
func Unmarshal(requestBody []byte, dest interface{}) error {
	// 1. 预处理JSON，将整数字段的空字符串替换为默认值
	processedJSON, err := preprocessJSON(requestBody, dest)
	if err != nil {
		return err
	}

	// 2. 使用预处理后的JSON进行解析
	if err := json.Unmarshal(processedJSON, dest); err != nil {
		return err
	}

	// 3. 设置其他默认值（处理字段不存在的情况）
	return setDefaultValues(reflect.ValueOf(dest).Elem(), processedJSON)
}

// preprocessJSON 预处理JSON，将整数字段的空字符串替换为默认值
func preprocessJSON(requestBody []byte, dest interface{}) ([]byte, error) {
	// 解析原始JSON到map
	var rawData map[string]interface{}
	if err := json.Unmarshal(requestBody, &rawData); err != nil {
		return requestBody, err
	}

	// 获取目标结构体的类型信息
	destType := reflect.TypeOf(dest)
	if destType.Kind() == reflect.Ptr {
		destType = destType.Elem()
	}

	// 遍历结构体字段，处理整数类型的空字符串
	processedFields := make(map[string]bool)
	processStructFields(destType, rawData, processedFields)

	// 重新序列化为JSON
	return json.Marshal(rawData)
}

// processStructFields 递归处理结构体字段（包括内联字段）
func processStructFields(structType reflect.Type, rawData map[string]interface{}, processedFields map[string]bool) {
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)

		// 处理内联字段
		if field.Anonymous {
			processStructFields(field.Type, rawData, processedFields)
			continue
		}

		// 获取字段名和默认值，优先使用json标签，其次使用query标签
		fieldName, defaultValue := getFieldNameAndDefault(field)
		if fieldName == "" || fieldName == "-" || processedFields[fieldName] {
			continue
		}
		processedFields[fieldName] = true

		// 检查字段是否为整数类型且值为空字符串
		if isIntegerType(field.Type.Kind()) {
			if rawValue, exists := rawData[fieldName]; exists {
				if strValue, ok := rawValue.(string); ok && strValue == "" {
					// 确定替换值
					replaceValue := defaultValue
					if replaceValue == "" {
						// 根据类型设置统一默认值
						if field.Type.Kind() >= reflect.Uint && field.Type.Kind() <= reflect.Uint64 {
							replaceValue = "0" // 无符号整数
						} else {
							replaceValue = "-1" // 有符号整数
						}
					}

					// 替换空字符串为默认值
					if intValue, err := strconv.ParseInt(replaceValue, 10, 64); err == nil {
						rawData[fieldName] = intValue
					}
				}
			}
		}
	}
}

// getFieldNameAndDefault 获取字段名和默认值，优先json标签，其次query标签
func getFieldNameAndDefault(field reflect.StructField) (string, string) {
	// 优先检查json标签
	if jsonTag := field.Tag.Get("json"); jsonTag != "" {
		tagParts := strings.Split(jsonTag, ",")
		fieldName := tagParts[0]
		if fieldName != "-" {
			defaultValue := ""
			for _, part := range tagParts[1:] {
				if strings.HasPrefix(part, "default=") {
					defaultValue = strings.TrimPrefix(part, "default=")
					break
				}
			}
			return fieldName, defaultValue
		}
	}

	// 检查query标签
	if queryTag := field.Tag.Get("query"); queryTag != "" {
		tagParts := strings.Split(queryTag, ",")
		fieldName := tagParts[0]
		if fieldName != "-" {
			defaultValue := ""
			for _, part := range tagParts[1:] {
				if strings.HasPrefix(part, "default=") {
					defaultValue = strings.TrimPrefix(part, "default=")
					break
				}
			}
			return fieldName, defaultValue
		}
	}

	return "", ""
}

// UniversalUnmarshal 通用参数解析函数，自动识别请求类型并解析参数
// 支持 JSON (POST)、Query Parameters (GET)、Form Data (POST) 等多种格式
func UniversalUnmarshal(ctx interface{}, dest interface{}) error {
	// 尝试从不同的上下文中提取请求信息
	if beegoCtx, ok := extractBeegoContext(ctx); ok {
		return unmarshalFromBeegoContext(beegoCtx, dest)
	}

	// 如果不是 Beego 上下文，尝试其他方式
	return fmt.Errorf("unsupported context type")
}

// extractBeegoContext 提取 Beego 上下文
func extractBeegoContext(ctx interface{}) (*context.Context, bool) {
	switch v := ctx.(type) {
	case *context.Context:
		return v, true
	default:
		// 尝试通过反射获取 Ctx 字段（适用于 Controller）
		val := reflect.ValueOf(ctx)
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}
		if val.Kind() == reflect.Struct {
			ctxField := val.FieldByName("Ctx")
			if ctxField.IsValid() && !ctxField.IsNil() {
				if beegoCtx, ok := ctxField.Interface().(*context.Context); ok {
					return beegoCtx, true
				}
			}
		}
	}
	return nil, false
}

// unmarshalFromBeegoContext 从 Beego 上下文解析参数
func unmarshalFromBeegoContext(ctx *context.Context, dest interface{}) error {
	method := ctx.Input.Method()
	contentType := ctx.Input.Header("Content-Type")

	switch {
	case method == "POST" && strings.Contains(contentType, "application/json"):
		// JSON 格式解析
		return UnmarshalJSON(ctx.Input.RequestBody, dest)

	case method == "GET" || (method == "POST" && strings.Contains(contentType, "application/x-www-form-urlencoded")):
		// 查询参数或表单数据解析
		return UnmarshalQuery(ctx, dest)

	default:
		return fmt.Errorf("unsupported request method/content-type: %s/%s", method, contentType)
	}
}

// UnmarshalJSON JSON格式解析（增强版）
func UnmarshalJSON(requestBody []byte, dest interface{}) error {
	return Unmarshal(requestBody, dest)
}

// UnmarshalQuery 查询参数解析（兼容旧版本）
func UnmarshalQuery(getQueryFunc interface{}, dest interface{}) error {
	// 检查参数类型，支持两种调用方式
	switch v := getQueryFunc.(type) {
	case *context.Context:
		// 新版本：传入 Beego Context
		return ParseQueryParams(dest, func(key string) string {
			return v.Input.Query(key)
		})
	case func(string, ...string) string:
		// 旧版本：传入 GetString 函数
		return ParseQueryParams(dest, func(key string) string {
			return v(key)
		})
	default:
		return fmt.Errorf("unsupported getQueryFunc type: %T", getQueryFunc)
	}
}
func setDefaultValues(val reflect.Value, rawJSON []byte) error {
	typ := val.Type()

	// 解析原始 JSON 数据
	var rawData map[string]interface{}
	if err := json.Unmarshal(rawJSON, &rawData); err != nil {
		return err
	}

	for i := 0; i < typ.NumField(); i++ {
		field := val.Field(i)
		structField := typ.Field(i)

		// 如果字段是未导出的，跳过
		if !field.CanSet() {
			continue
		}

		// 获取并解析 json 标签
		jsonTag := structField.Tag.Get("json")
		if jsonTag == "" {
			continue
		}

		// 解析标签
		tagParts := strings.Split(jsonTag, ",")
		fieldName := tagParts[0]
		if fieldName == "-" {
			continue
		}

		// 查找 default 值
		defaultValue := ""
		for _, part := range tagParts[1:] {
			if strings.HasPrefix(part, "default=") {
				defaultValue = strings.TrimPrefix(part, "default=")
				break
			}
		}

		// 处理嵌套结构
		switch field.Kind() {
		case reflect.Struct:
			// 如果是自定义时间类型，跳过
			if field.Type().String() == "types.MyUnixTime" ||
				field.Type().String() == "types.MyUnixDate" ||
				field.Type().String() == "types.MyUnixDateTime" {
				continue
			}
			// 递归处理嵌套结构
			if rawValue, exists := rawData[fieldName]; exists {
				rawFieldJSON, _ := json.Marshal(rawValue)
				setDefaultValues(field, rawFieldJSON)
			} else {
				// 如果嵌套结构不存在，也需要设置默认值
				setDefaultValues(field, []byte("{}"))
			}

		case reflect.Slice, reflect.Array:
			// 处理切片或数组中的结构体
			if field.Type().Elem().Kind() == reflect.Struct {
				if rawValue, exists := rawData[fieldName]; exists {
					rawSlice, ok := rawValue.([]interface{})
					if ok {
						for j := 0; j < field.Len(); j++ {
							if j < len(rawSlice) {
								rawItemJSON, _ := json.Marshal(rawSlice[j])
								setDefaultValues(field.Index(j), rawItemJSON)
							}
						}
					}
				}
			}
			// 如果是切片类型且为空，初始化为空切片
			if field.Kind() == reflect.Slice && field.IsNil() {
				field.Set(reflect.MakeSlice(field.Type(), 0, 0))
			}

		default:
			// 处理字段不存在或为空字符串的情况
			rawValue, exists := rawData[fieldName]

			// 检查是否需要设置默认值
			shouldSetDefault := false
			if !exists {
				// 字段不存在
				shouldSetDefault = true
			} else if exists {
				// 字段存在，检查是否为空字符串（针对整数类型）
				if strValue, ok := rawValue.(string); ok && strValue == "" {
					// 只对整数类型处理空字符串
					if isIntegerType(field.Kind()) {
						shouldSetDefault = true
					}
				}
			}

			if shouldSetDefault {
				// 处理整数类型（包括所有有符号和无符号整数）
				if isIntegerType(field.Kind()) {
					setIntegerDefault(field, defaultValue)
				} else {
					// 处理其他类型（保持原有逻辑）
					if defaultValue != "" {
						switch field.Kind() {
						case reflect.String:
							field.SetString(defaultValue)

						case reflect.Bool:
							value := defaultValue == "true"
							field.SetBool(value)

						case reflect.Float32, reflect.Float64:
							if floatValue, err := strconv.ParseFloat(defaultValue, 64); err == nil {
								field.SetFloat(floatValue)
							}
						}
					}
				}
			}
		}
	}

	return nil
}

// func setDefaultValues(val reflect.Value, rawJSON []byte) error {
// 	typ := val.Type()
//
// 	// 解析原始 JSON 数据
// 	var rawData map[string]interface{}
// 	if err := json.Unmarshal(rawJSON, &rawData); err != nil {
// 		return err
// 	}
//
// 	for i := 0; i < typ.NumField(); i++ {
// 		field := val.Field(i)
// 		structField := typ.Field(i)
//
// 		// 如果字段是未导出的，跳过
// 		if !field.CanSet() {
// 			continue
// 		}
//
// 		// 获取并解析 json 标签
// 		jsonTag := structField.Tag.Get("json")
// 		if jsonTag == "" {
// 			continue
// 		}
//
// 		// 解析标签
// 		tagParts := strings.Split(jsonTag, ",")
// 		fieldName := tagParts[0]
// 		if fieldName == "-" {
// 			continue
// 		}
//
// 		// 查找默认值设置
// 		defaultValue := ""
// 		for _, part := range tagParts[1:] {
// 			if strings.HasPrefix(part, "default=") {
// 				defaultValue = strings.TrimPrefix(part, "default=")
// 				break
// 			}
// 		}
//
// 		// 处理嵌套结构
// 		switch field.Kind() {
// 		case reflect.Struct:
// 			// 如果是自定义时间类型，跳过
// 			if field.Type().String() == "pkg.MyUnixTime" {
// 				continue
// 			}
// 			// 递归处理嵌套结构
// 			if rawValue, exists := rawData[fieldName]; exists {
// 				rawFieldJSON, _ := json.Marshal(rawValue)
// 				setDefaultValues(field, rawFieldJSON)
// 			} else {
// 				// 如果嵌套结构不存在，也需要设置默认值
// 				setDefaultValues(field, []byte("{}"))
// 			}
//
// 		case reflect.Slice, reflect.Array:
// 			// 处理切片或数组中的结构体
// 			if field.Type().Elem().Kind() == reflect.Struct {
// 				if rawValue, exists := rawData[fieldName]; exists {
// 					rawSlice, ok := rawValue.([]interface{})
// 					if ok {
// 						for j := 0; j < field.Len(); j++ {
// 							if j < len(rawSlice) {
// 								rawItemJSON, _ := json.Marshal(rawSlice[j])
// 								setDefaultValues(field.Index(j), rawItemJSON)
// 							}
// 						}
// 					}
// 				}
// 			}
// 			// 如果是切片类型且为空，初始化为空切片
// 			if field.Kind() == reflect.Slice && field.IsNil() {
// 				field.Set(reflect.MakeSlice(field.Type(), 0, 0))
// 			}
//
// 		default:
// 			// 如果字段不存在于 JSON 中，设置默认值
// 			if _, exists := rawData[fieldName]; !exists {
// 				switch field.Kind() {
// 				case reflect.Int, reflect.Int64:
// 					var value int64 = -1 // 默认的默认值
// 					if defaultValue != "" {
// 						if intValue, err := strconv.ParseInt(defaultValue, 10, 64); err == nil {
// 							value = intValue
// 						}
// 					}
// 					field.SetInt(value)
//
// 				case reflect.String:
// 					field.SetString(defaultValue)
//
// 				case reflect.Bool:
// 					value := false
// 					if defaultValue == "true" {
// 						value = true
// 					}
// 					field.SetBool(value)
//
// 				case reflect.Float64:
// 					var value float64
// 					if defaultValue != "" {
// 						if floatValue, err := strconv.ParseFloat(defaultValue, 64); err == nil {
// 							value = floatValue
// 						}
// 					}
// 					field.SetFloat(value)
// 				}
// 			}
// 		}
// 	}
//
// 	return nil
// }

// func UnmarshalQuery(getQueryFunc func(string, ...string) string, dest interface{}) error {
// 	val := reflect.ValueOf(dest).Elem()
// 	typ := val.Type()
//
// 	for i := 0; i < typ.NumField(); i++ {
// 		field := val.Field(i)
// 		structField := typ.Field(i)
//
// 		// 获取 query tag
// 		queryTag := structField.Tag.Get("query")
// 		if queryTag == "" {
// 			continue
// 		}
//
// 		// 解析 tag
// 		tagParts := strings.Split(queryTag, ",")
// 		paramName := tagParts[0]
// 		defaultValue := ""
//
// 		// 查找 default 值
// 		for _, part := range tagParts[1:] {
// 			if strings.HasPrefix(part, "default=") {
// 				defaultValue = strings.TrimPrefix(part, "default=")
// 				break
// 			}
// 		}
//
// 		// 获取参数值
// 		strValue := getQueryFunc(paramName)
//
// 		// 处理不同类型
// 		switch field.Kind() {
// 		case reflect.String:
// 			if strValue == "" {
// 				field.SetString(defaultValue)
// 			} else {
// 				field.SetString(strValue)
// 			}
//
// 		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
// 			var value int64
// 			var bitSize int
// 			switch field.Kind() {
// 			case reflect.Int8:
// 				bitSize = 8
// 			case reflect.Int16:
// 				bitSize = 16
// 			case reflect.Int32:
// 				bitSize = 32
// 			default:
// 				bitSize = 64
// 			}
//
// 			// 如果有传入值，尝试解析
// 			if strValue != "" {
// 				if intValue, err := strconv.ParseInt(strValue, 10, bitSize); err == nil {
// 					value = intValue
// 					field.SetInt(value)
// 					continue
// 				}
// 			}
//
// 			// 如果没有传入值或解析失败，且有默认值，使用默认值
// 			if defaultValue != "" {
// 				if defaultInt, err := strconv.ParseInt(defaultValue, 10, bitSize); err == nil {
// 					value = defaultInt
// 				}
// 			}
// 			field.SetInt(value)
// 		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
// 			var value uint64
// 			var bitSize int
// 			switch field.Kind() {
// 			case reflect.Uint8:
// 				bitSize = 8
// 			case reflect.Uint16:
// 				bitSize = 16
// 			case reflect.Uint32:
// 				bitSize = 32
// 			default:
// 				bitSize = 64
// 			}
//
// 			// 如果有传入值，尝试解析
// 			if strValue != "" {
// 				if uintValue, err := strconv.ParseUint(strValue, 10, bitSize); err == nil {
// 					value = uintValue
// 					field.SetUint(value)
// 					continue
// 				}
// 			}
//
// 			// 如果没有传入值或解析失败，且有默认值，使用默认值
// 			if defaultValue != "" {
// 				if defaultUint, err := strconv.ParseUint(defaultValue, 10, bitSize); err == nil {
// 					value = defaultUint
// 				}
// 			}
// 			field.SetUint(value)
// 		case reflect.Bool:
// 			var value bool
// 			if strValue != "" {
// 				value = strValue == "true" || strValue == "1"
// 			} else if defaultValue != "" {
// 				value = defaultValue == "true" || defaultValue == "1"
// 			}
// 			field.SetBool(value)
//
// 		case reflect.Struct:
// 			// 处理自定义时间类型
// 			switch field.Type().String() {
// 			case "pkg.MyUnixDate":
// 				if strValue != "" {
// 					timestamp, err := strconv.ParseInt(strValue, 10, 64)
// 					if err == nil && timestamp != 0 {
// 						t := time.Unix(timestamp, 0)
// 						newDate := types.MyUnixDate{MyTimestamp: time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)}
// 						field.Set(reflect.ValueOf(newDate))
// 					} else {
// 						// 解析失败或timestamp为0时，设置为零值
// 						field.Set(reflect.ValueOf(types.MyUnixDate{MyTimestamp: time.Time{}}))
// 					}
// 				} else {
// 					// 没有传值时，设置为零值
// 					field.Set(reflect.ValueOf(types.MyUnixDate{MyTimestamp: time.Time{}}))
// 				}
//
// 			case "pkg.MyUnixTime":
// 				if strValue != "" {
// 					timestamp, err := strconv.ParseInt(strValue, 10, 64)
// 					if err == nil && timestamp != 0 {
// 						t := time.Unix(timestamp, 0)
// 						newTime := types.MyUnixTime{MyTimestamp: time.Date(0, 1, 1, t.Hour(), t.Minute(), t.Second(), 0, time.Local)}
// 						field.Set(reflect.ValueOf(newTime))
// 					} else {
// 						// 解析失败或timestamp为0时，设置为零值
// 						field.Set(reflect.ValueOf(types.MyUnixTime{MyTimestamp: time.Time{}}))
// 					}
// 				} else {
// 					// 没有传值时，设置为零值
// 					field.Set(reflect.ValueOf(types.MyUnixTime{MyTimestamp: time.Time{}}))
// 				}
//
// 			case "pkg.MyUnixDateTime":
// 				if strValue != "" {
// 					timestamp, err := strconv.ParseInt(strValue, 10, 64)
// 					if err == nil && timestamp != 0 {
// 						newDateTime := types.MyUnixDateTime{MyTimestamp: time.Unix(timestamp, 0)}
// 						field.Set(reflect.ValueOf(newDateTime))
// 					} else {
// 						// 解析失败或timestamp为0时，设置为零值
// 						field.Set(reflect.ValueOf(types.MyUnixDateTime{MyTimestamp: time.Time{}}))
// 					}
// 				} else {
// 					// 没有传值时，设置为零值
// 					field.Set(reflect.ValueOf(types.MyUnixDateTime{MyTimestamp: time.Time{}}))
// 				}
// 			}
// 		}
// 	}
//
// 	return nil
// }

// ParseQueryParams 解析查询参数到结构体
func ParseQueryParams(dest interface{}, getQueryFunc func(string) string) error {
	val := reflect.ValueOf(dest).Elem()
	typ := val.Type()

	// 处理具体类型的函数
	handleValue := func(field reflect.Value, strValue, defaultValue string) {
		switch field.Kind() {
		case reflect.String:
			if strValue == "" {
				field.SetString(defaultValue)
			} else {
				field.SetString(strValue)
			}

		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			var value int64
			var bitSize int
			switch field.Kind() {
			case reflect.Int8:
				bitSize = 8
			case reflect.Int16:
				bitSize = 16
			case reflect.Int32:
				bitSize = 32
			default:
				bitSize = 64
			}

			if strValue != "" {
				if intValue, err := strconv.ParseInt(strValue, 10, bitSize); err == nil {
					value = intValue
					field.SetInt(value)
					return
				}
			}

			if defaultValue != "" {
				if defaultInt, err := strconv.ParseInt(defaultValue, 10, bitSize); err == nil {
					value = defaultInt
				}
			} else {
				// 如果没有明确的 default 值，有符号整型默认使用 -1
				value = -1
			}
			field.SetInt(value)

		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			var value uint64
			var bitSize int
			switch field.Kind() {
			case reflect.Uint8:
				bitSize = 8
			case reflect.Uint16:
				bitSize = 16
			case reflect.Uint32:
				bitSize = 32
			default:
				bitSize = 64
			}

			if strValue != "" {
				if uintValue, err := strconv.ParseUint(strValue, 10, bitSize); err == nil {
					value = uintValue
					field.SetUint(value)
					return
				}
			}

			if defaultValue != "" {
				if defaultUint, err := strconv.ParseUint(defaultValue, 10, bitSize); err == nil {
					value = defaultUint
				}
			}
			field.SetUint(value)

		case reflect.Bool:
			var value bool
			if strValue != "" {
				value = strValue == "true" || strValue == "1"
			} else if defaultValue != "" {
				value = defaultValue == "true" || defaultValue == "1"
			}
			field.SetBool(value)

		case reflect.Struct:
			// 处理自定义时间类型
			switch field.Type().String() {
			case "types.MyUnixDate":
				if strValue != "" {
					timestamp, err := strconv.ParseInt(strValue, 10, 64)
					if err == nil && timestamp != 0 {
						t := time.Unix(timestamp, 0)
						newDate := types.MyUnixDate{MyTimestamp: time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)}
						field.Set(reflect.ValueOf(newDate))
					} else {
						// 解析失败或timestamp为0时，设置为零值
						field.Set(reflect.ValueOf(types.MyUnixDate{MyTimestamp: time.Time{}}))
					}
				} else {
					// 没有传值时，设置为零值
					field.Set(reflect.ValueOf(types.MyUnixDate{MyTimestamp: time.Time{}}))
				}

			case "types.MyUnixTime":
				if strValue != "" {
					timestamp, err := strconv.ParseInt(strValue, 10, 64)
					if err == nil && timestamp != 0 {
						t := time.Unix(timestamp, 0)
						newTime := types.MyUnixTime{MyTimestamp: time.Date(0, 1, 1, t.Hour(), t.Minute(), t.Second(), 0, time.Local)}
						field.Set(reflect.ValueOf(newTime))
					} else {
						// 解析失败或timestamp为0时，设置为零值
						field.Set(reflect.ValueOf(types.MyUnixTime{MyTimestamp: time.Time{}}))
					}
				} else {
					// 没有传值时，设置为零值
					field.Set(reflect.ValueOf(types.MyUnixTime{MyTimestamp: time.Time{}}))
				}

			case "types.MyUnixDateTime":
				if strValue != "" {
					timestamp, err := strconv.ParseInt(strValue, 10, 64)
					if err == nil && timestamp != 0 {
						newDateTime := types.MyUnixDateTime{MyTimestamp: time.Unix(timestamp, 0)}
						field.Set(reflect.ValueOf(newDateTime))
					} else {
						// 解析失败或timestamp为0时，设置为零值
						field.Set(reflect.ValueOf(types.MyUnixDateTime{MyTimestamp: time.Time{}}))
					}
				} else {
					// 没有传值时，设置为零值
					field.Set(reflect.ValueOf(types.MyUnixDateTime{MyTimestamp: time.Time{}}))
				}
			}
		}
	}

	for i := 0; i < typ.NumField(); i++ {
		field := val.Field(i)
		structField := typ.Field(i)

		// 获取字段名和默认值，支持混合标签类型
		paramName, defaultValue := getFieldNameAndDefault(structField)
		if paramName == "" || paramName == "-" {
			continue
		}

		// 获取参数值
		strValue := getQueryFunc(paramName)

		switch field.Kind() {
		case reflect.Ptr:
			// 如果没有值和默认值，保持为 nil
			if strValue == "" && defaultValue == "" {
				continue
			}
			// 创建新的值
			if field.IsNil() {
				field.Set(reflect.New(field.Type().Elem()))
			}
			// 使用相同的处理函数处理指针指向的值
			handleValue(field.Elem(), strValue, defaultValue)

		default:
			// 直接处理非指针类型
			handleValue(field, strValue, defaultValue)
		}
	}

	return nil
}

// isIntegerType 检查是否为整数类型（包括所有有符号和无符号整数）
func isIntegerType(kind reflect.Kind) bool {
	switch kind {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return true
	default:
		return false
	}
}

// setIntegerDefault 为整数类型字段设置默认值
func setIntegerDefault(field reflect.Value, defaultValue string) {
	// 确定要设置的默认值
	targetValue := defaultValue
	if targetValue == "" {
		// 如果没有定义default tag，统一设置为 -1
		targetValue = "-1"
	}

	// 根据字段类型进行安全的类型转换和设置
	switch field.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		// 有符号整数类型
		if intValue, err := strconv.ParseInt(targetValue, 10, 64); err == nil {
			// 检查值是否在字段类型的范围内
			if isIntValueInRange(intValue, field.Kind()) {
				field.SetInt(intValue)
			} else {
				// 如果超出范围，设置为 -1
				field.SetInt(-1)
			}
		} else {
			// 解析失败，设置为 -1
			field.SetInt(-1)
		}

	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		// 无符号整数类型
		if uintValue, err := strconv.ParseUint(targetValue, 10, 64); err == nil {
			// 检查值是否在字段类型的范围内
			if isUintValueInRange(uintValue, field.Kind()) {
				field.SetUint(uintValue)
			} else {
				// 如果超出范围，设置为 0（无符号类型不能为负数）
				field.SetUint(0)
			}
		} else {
			// 解析失败或为负数，设置为 0
			field.SetUint(0)
		}
	}
}

// isIntValueInRange 检查有符号整数值是否在指定类型的范围内
func isIntValueInRange(value int64, kind reflect.Kind) bool {
	switch kind {
	case reflect.Int8:
		return value >= -128 && value <= 127
	case reflect.Int16:
		return value >= -32768 && value <= 32767
	case reflect.Int32:
		return value >= -2147483648 && value <= 2147483647
	case reflect.Int, reflect.Int64:
		// int 和 int64 可以容纳 int64 的所有值
		return true
	default:
		return false
	}
}

// isUintValueInRange 检查无符号整数值是否在指定类型的范围内
func isUintValueInRange(value uint64, kind reflect.Kind) bool {
	switch kind {
	case reflect.Uint8:
		return value <= 255
	case reflect.Uint16:
		return value <= 65535
	case reflect.Uint32:
		return value <= 4294967295
	case reflect.Uint, reflect.Uint64:
		// uint 和 uint64 可以容纳 uint64 的所有值
		return true
	default:
		return false
	}
}
