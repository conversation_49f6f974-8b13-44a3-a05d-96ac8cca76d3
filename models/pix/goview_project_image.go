package pix

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// GoViewProjectImage GoView项目图片统一管理模型
type GoViewProjectImage struct {
	ID           int64     `orm:"column(id);pk;auto" json:"id"`
	ProjectID    string    `orm:"column(project_id)" json:"projectId"`
	FileName     string    `orm:"column(file_name)" json:"fileName"`
	FileURL      string    `orm:"column(file_url)" json:"fileUrl"`
	FileSize     int64     `orm:"column(file_size)" json:"fileSize"`
	Tags         string    `orm:"column(tags);type(json)" json:"tags"` // JSON数组存储标签
	IsActive     bool      `orm:"column(is_active);default(false)" json:"isActive"`
	CreateTime   time.Time `orm:"column(create_time);auto_now_add;type(datetime)" json:"createTime"`
	UpdateTime   time.Time `orm:"column(update_time);auto_now;type(datetime)" json:"updateTime"`
	CreateUserID string    `orm:"column(create_user_id)" json:"createUserId"`
}

// TableName 指定表名
func (g *GoViewProjectImage) TableName() string {
	return "goview_project_images"
}

func init() {
	// 注册模型
	orm.RegisterModel(new(GoViewProjectImage))
}
