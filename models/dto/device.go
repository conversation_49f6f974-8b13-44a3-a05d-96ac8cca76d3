package dto

import (
	"ccapi/models/pix"
	"ccapi/pkg/types"
)

// RespDeviceForm 设备返回信息
type RespDeviceForm struct {
	ID          int64  `json:"id"`
	IMEI        string `json:"imei"`
	IMSI        string `json:"imsi"`
	BindRemark  string `json:"bind_remark"`
	IP          string `json:"ip"`
	Name        string `json:"name"`
	Vender      string `json:"vender"`
	CI          string `json:"ci"`
	PCI         string `json:"pci"`
	ManageIp    string `json:"manage_ip"`
	Model       string `json:"model"`
	SoftwareVer string `json:"software_ver"`
	HardwareVer string `json:"hardware_ver"`
	UID         string `json:"uid"`
	UserId      int    `json:"user_id"`
	RSRP        int64  `json:"rsrp"`
	SINR        int64  `json:"sinr"`
	RSSI        int64  `json:"rssi"`
	HistoryDays int    `json:"historyDays"`
	RSRQ        int64  `json:"rsrq"`
	Online      int    `json:"online"`
	// Warning         int    `json:"warning"`       // WAN 口告警状态
	OnlineBigint    int64                `json:"online_bigint"` // 本次在线时长
	TimeSpending    int64                `json:"time_spending"` // 本周在线时长
	OnlineTimes     int64                `json:"online_times"`  // 上线次数
	OfflineTimes    int64                `json:"offline_times"` // 下线次数
	OnlineTime      types.MyUnixDateTime `json:"online_time"`
	OfflineTime     types.MyUnixDateTime `json:"offline_time"`
	CreatedTime     types.MyUnixDateTime `json:"created_time"`
	UpdatedTime     types.MyUnixDateTime `json:"updated_time"`
	Longitude       string               `json:"longitude"`
	Latitude        string               `json:"latitude"`
	Position        string               `json:"position"`
	WanRx           int64                `json:"wan_rx"`
	WanTx           int64                `json:"wan_tx"`
	UpgradeState    string               `json:"upgrade_state"`
	UpgradeProgress string               `json:"upgrade_progress"`

	/*
		Located bool  `json:"located"`
		Sta     uint8 `json:"sta"`
		Mode    uint8 `json:"mode"`
	*/
	Acc           int8          `json:"acc"`        /* ACC状态,即钥匙状态; 整型数S8; 0:ACC off, 1:ACC on, -1:未知; */
	Gear          int8          `json:"gear"`       /* 档位; 整型数S8; 0:P档, 2:R档, 3:N档, 4:D档, -1:未知; */
	Door          int8          `json:"door"`       /* 车门开关状态; 整型数S8; 0:关, 1:开, -1:未知; */
	Light         int8          `json:"light"`      /* 灯光开关状态; 整型数S8; 0:关, 1:开, -1:未知; */
	Win           int8          `json:"win"`        /* 车窗开关状态; 整型数S8; 0:关, 1:开, -1:未知; */
	Tm            int32         `json:"tm"`         /* 总里程(total mileages); 整型数S32; 单位km; -1:未知; */
	Rm            int16         `json:"rm"`         /* 当前电量可跑的续航里程; 整型数S16; 单位km; -1:未知; */
	Ste           float32       `json:"ste"`        /* 方向盘(steering wheel)转角; 浮点数S16; 单位0.1度; -1:未知 */
	Brk           int16         `json:"brk"`        /* 制动(braking)值; 整型数S16; 单位0.1Mp; 保留1位小数; -1:未知*/
	HeadMode      uint8         `json:"headMode"`   /* 当前转向模式; 整型数U8; 1:前后异向模式, 2:常规模式, 3：前后同向模式; */
	Sgn           uint8         `json:"sgn"`        /* 4G/5G网络信号强度百分比; 整型数U8; */
	Spd           int16         `json:"spd"`        /* 车速; 整型数S16; 单位km/h; -1:未知; */
	SpdL          int16         `json:"spdL"`       /* 最高限速; 整型数S16; 单位km/h; -1:未知; */
	PL            int8          `json:"pL"`         /* 剩余电量百分比; 整型数S8; -1:未知; */
	PV            int16         `json:"pV"`         /* 大电瓶电压; 整型数S16; 单位0.1V; -1:未知; */
	PC            int16         `json:"pC"`         /* 总电流; 整型数S16; 单位0.1A; -1:未知 */
	PCh           int8          `json:"pCh"`        /* 充电状态; 整型数S8; 0:未充电, 1:充电中, -1:未知; */
	Bat           int16         `json:"bat"`        /* 小电瓶电压; 整型数S16; 单位0.1V; -1:未知态; */
	Located       int           `json:"located"`    /* GPS是否定位; bool型; true:定位, false:未定位; */
	Lng           float64       `json:"lng"`        /* GPS经度; 浮点数Float64; 单位度; 保留6位小数; */
	Lat           float64       `json:"lat"`        /* GPS纬度; 浮点数Float64; 单位度; 保留6位小数; */
	Alt           float32       `json:"alt"`        /* GPS海拔; 浮点数Float32; 单位米; 保留1位小数; */
	Angle         float32       `json:"angle"`      /* GPS航向角度; 浮点数Float32; 单位度; 保留1位小数; */
	SatCnt        uint8         `json:"satCnt"`     /* 有效GPS卫星数; 整型数; */
	Mode          uint8         `json:"mode"`       /* 车辆驾驶模式; 0:空闲状态(idle), 1:自动驾驶模式中, 2:遥控模式, 3:路径规划中; */
	Sta           uint8         `json:"sta"`        /* 车辆状态; 整型数U8; 0:正常, 1:异常; */
	Err           string        `json:"err"`        /* 车辆状态异常原因; "":无异常, 非空字符串:异常原因; */
	Event         uint8         `json:"event"`      /* 触发此车辆实时状态消息上报的原因; 整型数; 取值见下表event取值对照表; */
	Warning       []int         `json:"warning"`    /* 无符号32位整型 报警标志位;取值详见报警预警标志位定义*/
	Ts            int64         `json:"ts"`         /* 消息时间戳Unix时间 单位秒 */
	LatestTask    LatestTask    `json:"latestTask"` /* 车辆最近的任务 */
	VideoName     string        `json:"video_name"` /*视频名称*/
	VideoUrl      string        `json:"video_url"`  /*视频下载地址*/
	Advert        string        `json:"advert"`     /*广告地址*/
	AdvertType    int           `json:"advert_type"`
	VSta          int           `json:"vSta"`
	MapId         int           `json:"map_id"`
	Task          pix.Task      `json:"task"`
	TaskChild     pix.TaskChild `json:"task_child"`
	Type          int           `json:"type"` // 车辆类型
	Thr           float32       `json:"thr"`
	ExpireTime    int64         `json:"expire_time"`
	EmgSta        uint8         `json:"emgSta"`
	AirCon        uint8         `json:"airCon"`
	InTemp        uint8         `json:"inTemp"`
	OutTemp       uint8         `json:"outTemp"`
	Smoke         int32         `json:"smoke"`
	Co2           int32         `json:"co2"`
	Seatbelt      uint8         `json:"seatbelt"`
	RemoteDriving int           `json:"remote_driving"`
	VinLogin      int           `json:"vin_login"`
	Push          int           `json:"push"`
	Rc            int           `json:"rc"`
}

// AddDevice 新增设备
type AddDevice struct {
	IMEI          string `json:"imei" validate:"required"`
	IMSI          string `json:"imsi" validate:"required"`
	Name          string `json:"name"`
	UID           int    `json:"uid"`
	Type          int    `json:"type"`
	BindRemark    string `json:"bind_remark"`
	Real          int    `json:"real"`
	ExpireTime    int    `json:"expire_time"`
	RemoteDriving int    `json:"remote_driving"`
	VinLogin      int    `json:"vin_login"`
	MapId         int    `json:"map_id"`
	Push          int    `json:"push"`
	HistoryDays   int    `json:"historyDays" validate:"gte=0" default:"180"` // 历史数据保留天数,默认180天
}

// UpdateDevice 修改设备
type UpdateDevice struct {
	Id            int    `json:"id" validate:"required"`
	Name          string `json:"name" validate:"required"`
	Uid           int    `json:"uid" validate:"gte=0"`
	MaintainState int    `json:"maintain_state"` // 维护状态
	Type          int    `json:"type"`
	Real          int    `json:"real"`
	ExpireTime    int64  `json:"expire_time"`
	BindRemark    string `json:"bind_remark"`
	RemoteDriving int    `json:"remote_driving"`
	VinLogin      int    `json:"vin_login"`
	Push          int    `json:"push"`
	HistoryDays   int    `json:"historyDays" validate:"gte=0"` // 历史数据保留天数
	MapId         int    `json:"map_id"`
}

type RedisHeart struct {
	Cmd        int        `json:"cmd"`
	Uid        string     `json:"uid"`
	VType      int        `json:"vType"`
	Acc        int        `json:"acc"`
	Gear       int        `json:"gear"`
	Door       int        `json:"door"`
	Light      int        `json:"light"`
	Win        int        `json:"win"`
	Tm         int        `json:"tm"`
	Rm         int        `json:"rm"`
	Ste        float32    `json:"ste"`
	Brk        float64    `json:"brk"`
	Thr        float64    `json:"thr"`
	HeadMode   int        `json:"headMode"`
	Sgn        int        `json:"sgn"`
	Spd        int        `json:"spd"`
	PL         int        `json:"pL"`
	PV         int        `json:"pV"`
	PC         int        `json:"pC"`
	PCh        int        `json:"pCh"`
	Bat        int        `json:"bat"`
	Located    int        `json:"located"`
	Lng        float64    `json:"lng"`
	Lat        float64    `json:"lat"`
	Alt        float64    `json:"alt"`
	Angle      float64    `json:"angle"`
	SatCnt     int        `json:"satCnt"`
	PosQ       float64    `json:"posQ"`
	Mode       int        `json:"mode"`
	Sta        int        `json:"sta"`
	Err        string     `json:"err"`
	Event      int        `json:"event"`
	Warning    []int      `json:"warning"`
	VSta       int        `json:"vSta"`
	LatestTask LatestTask `json:"latestTask"`
	Ts         int        `json:"ts"`
}

type CurrentStation struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type TargetStation struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type LatestTask struct {
	Step           int8           `json:"step"`
	RouteId        int            `json:"routeId"`
	TaskId         int            `json:"taskId"`
	Progress       int8           `json:"progress"`
	StartTime      int64          `json:"startTime"`
	Name           string         `json:"name"`
	Desp           string         `json:"desp"`
	CurrentStation CurrentStation `json:"currentStation"`
	TargetStation  TargetStation  `json:"targetStation"`
	LeftTime       int            `json:"leftTime"`
	TargetDistance int            `json:"targetDistance"`
	TaskDistance   int            `json:"taskDistance"`
	ActionList     []ActionList   `json:"actionList"`
	Other          string         `json:"other"`
}

type ActionList struct {
	Step   int    `json:"step"`
	Action string `json:"action"`
	PST    int    `json:"pST"`
	PET    int    `json:"pET"`
	ST     int    `json:"sT"`
	ET     int    `json:"eT"`
	Prog   int    `json:"prog"`
}

type TaskContent struct {
	Action  string `json:"action"`
	Content string `json:"content"`
	PET     int    `json:"pET"`
	PST     int    `json:"pST"`
}

type Lan struct {
	LinkStatus int    `json:"link_status"`
	Name       string `json:"name"`
}

type DeviceCmd struct {
	Id  int64 `json:"id" validate:"required"`
	Cmd int   `json:"cmd" validate:"required"`
}

type DeviceCmds struct {
	Id []int64 `json:"id" validate:"required"`
}

type SetMap struct {
	Imsi string `json:"imsi"`
	Name string `json:"name"`
}

type GoPoint struct {
	Id  int     `json:"id"`
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
	Raw string  `json:"raw"`
}

type SetSpeed struct {
	Imsi  string `json:"imsi"`
	Speed int    `json:"speed"`
}

type Start struct {
	VehicleId    int `json:"vehicleId"`
	TargetStopId int `json:"targetStopId"`
	MapId        int `json:"mapId"`
}

type TaskPost struct {
	TaskId int `json:"taskId"`
}

type VehiclePost struct {
	VehicleId int `json:"vehicleId"`
}

// type SpGoPoint struct {
//	Name         string   `json:"name"`
//	PointId      int      `json:"point_id"`
//	Lat          float64  `json:"lat"`
//	Lng          float64  `json:"lng"`
//	Raw          string   `json:"raw"`
//	SpActionType int      `json:"sp_action_type"`
//	ActionList   []string `json:"action_list"`
// }

type TaskInfo struct {
	Action  string `json:"action"`
	Content string `json:"content"`
}

type Task struct {
	Id         int        `json:"id"`
	RouteId    int        `json:"route_id"`
	Name       string     `json:"name"`
	Desp       string     `json:"desp"`
	ActionList []TaskInfo `json:"action_list"`
}

type AnalysisUser struct {
	No           int    `json:"no"`
	Uid          int64  `json:"uid"`
	Username     string `json:"username"`
	Total        int    `json:"total"`
	Online       int    `json:"online"`
	Offline      int    `json:"offline"`
	Notification int    `json:"notification"`
}

type DeviceMileage struct {
	Id       int    `json:"id"`
	Imsi     string `json:"imsi"`
	Name     string `json:"name"`
	Uid      int    `json:"uid"`
	Username string `json:"username"`
	Mileage  int    `json:"mileage"`
}

type DeviceMessage struct {
	Id       int    `json:"id"`
	Imsi     string `json:"imsi"`
	Name     string `json:"name"`
	Uid      int    `json:"uid"`
	Username string `json:"username"`
	Num      int    `json:"num"`
}

type Device struct {
	Id             int    `json:"id"`
	Imei           string `json:"imei"`
	Ip             string `json:"ip"`
	Model          string `json:"model"`
	SoftwareVer    string `json:"software_ver"`
	HardwareVer    string `json:"hardware_ver"`
	Uid            int    `json:"uid"`
	Vender         string `json:"vender"`
	Ci             string `json:"ci"`
	Pci            string `json:"pci"`
	ManageIp       string `json:"manage_ip"`
	Position       string `json:"position"`
	Longitude      string `json:"longitude"`
	Latitude       string `json:"latitude"`
	Status         int    `json:"status"`
	MaintainState  int    `json:"maintain_state"`
	TimeSpending   int    `json:"time_spending"`
	OnlineBigint   int    `json:"online_bigint"`
	OnlineTimes    int    `json:"online_times"`
	OfflineTimes   int    `json:"offline_times"`
	Online         int    `json:"online"`
	Name           string `json:"name"`
	Tz             string `json:"tz"`
	NtpServer      string `json:"ntp_server"`
	Interval       int    `json:"interval"`
	MonitoringIp   string `json:"monitoring_ip"`
	MonitoringPort int    `json:"monitoring_port"`
	UnmpIp         string `json:"unmp_ip"`
	UnmpPort       int    `json:"unmp_port"`
	OnlineTime     string `json:"online_time"`
	OfflineTime    string `json:"offline_time"`
	CreatedTime    string `json:"created_time"`
	UpdatedTime    string `json:"updated_time"`
	Imsi           string `json:"imsi"`
	ResetTime      int64  `json:"reset_time"`
	OperationNum   int    `json:"operation_num"`
	OutTime        int64  `json:"out_time"`
	Password       string `json:"password"`
	Advert         string `json:"advert"`
	AdvertType     int    `json:"advert_type"`
	TaskId         int    `json:"task_id"`
	AdvertSetId    int    `json:"advert_set_id"`
	Type           int    `json:"type"`
	Real           int    `json:"real"`
	ExpireTime     int    `json:"expire_time"`
	MapId          int    `json:"map_id"`
	Province       string `json:"province"`
	City           string `json:"city"`
	Area           string `json:"area"`
	Username       string `json:"username"`
}

type Vin struct {
	Imsi          string `json:"imsi"`
	VinLogin      int    `json:"vin_login"`
	VinCode       string `json:"vin_code"`
	RemoteDriving int    `json:"remote_driving"`
}

type UpdateVin struct {
	Imsi     string `json:"imsi"`
	VinCode  string `json:"vin_code"`
	Password string `json:"password"`
}

type UpdatePushConfig struct {
	Imsi     string `json:"imsi"`
	Position int    `json:"position"`
	Open     int    `json:"open"`
	Type     int    `json:"type"`
}

type PushData struct {
	Id        int    `json:"id"`
	DeviceId  int    `json:"device_id"`
	Imsi      string `json:"imsi"`
	Position  int    `json:"position"`
	Size      int    `json:"size"`
	Url       string `json:"url"`
	Type      int    `json:"type"`
	BeginTime int64  `json:"begin_time"`
	EndTime   int64  `json:"end_time"`
}

type Push struct {
	Imsi     string `json:"imsi"`
	Position int    `json:"position"`
	Pwd      string `json:"pwd"`
}

// LoginWithCaptcha 带验证码的登录请求
type LoginWithCaptcha struct {
	Username    string `json:"username" valid:"Required"`
	Password    string `json:"password" valid:"Required"`
	CaptchaId   string `json:"captchaId" valid:"Required"`
	CaptchaCode string `json:"captchaCode" valid:"Required"`
}

// DeviceListQuery 设备列表查询参数
type DeviceListQuery struct {
	Page       int    `query:"page,default=1"`
	Limit      int    `query:"limit,default=10"`
	Uid        int    `query:"uid,default=-1"`
	Online     int    `query:"online,default=-1"`
	Imsi       string `query:"imsi"`
	Imei       string `query:"imei"`
	IsVcu      int    `query:"is_vcu,default=-1"`
	Type       int    `query:"type,default=-1"`
	City       string `query:"city"`
	Area       string `query:"area"`
	VinLogin   int    `query:"vin_login,default=-1"`
	Name       string `query:"name"`
	BindRemark string `query:"bind_remark"`
	MapId      int    `query:"map_id,default=0"`
}
