package dto

import (
	"ccapi/pkg/types"
)

// AreaDistribution 区域分布统计
type AreaDistribution struct {
	Total     int                       `json:"total"`     // 总数
	Areas     map[string]*ProvinceStats `json:"areas"`     // 中国各省份统计
	Countries map[string]int            `json:"countries"` // 各国家统计
}

// ProvinceStats 省份统计
type ProvinceStats struct {
	Name   string                `json:"name"`   // 省份名称
	Total  int                   `json:"total"`  // 该省总数
	Cities map[string]*CityStats `json:"cities"` // 城市统计
	Other  int                   `json:"other"`  // 未标记城市的数量
}

// CityStats 城市统计
type CityStats struct {
	Name  string         `json:"name"`  // 城市名称
	Total int            `json:"total"` // 该市总数
	Areas map[string]int `json:"areas"` // 区域统计
	Other int            `json:"other"` // 未标记区域的数量
}

// StatisticsQuery 统计查询参数
type StatisticsQuery struct {
	UID    int64  `json:"uid" query:"uid"`                  // 用户ID
	AllUID string `json:"allUid" query:"allUid"`            // 所有用户ID，逗号分隔
	Online int    `json:"online" query:"online"` // 在线状态(0离线1在线-1不限)
	IMSI   string `json:"imsi" query:"imsi"`                // 终端编号
	Type   int    `json:"type" query:"type"`     // 车辆类型(0-4,-1不限)
	Mode   int    `json:"mode" query:"mode"`     // 驾驶模式(-1:不限 99:故障状态 1:手动遥控 2:待机状态 3:自动驾驶 4:远程驾驶 5:安全接管)
}

// DeviceStatus 设备状态
type DeviceStatus struct {
	ID          int64                `json:"id"`
	IMSI        string               `json:"imsi"`
	Online      int                  `json:"online"`
	Province    string               `json:"province"`
	City        string               `json:"city"`
	Area        string               `json:"area"`
	Country     string               `json:"country"`     // 国家
	Type        int                  `json:"type"`        // 车辆类型
	Mode        int                  `json:"mode"`        // 驾驶模式
	Speed       int                  `json:"speed"`       // 车速
	Gear        int                  `json:"gear"`        // 档位
	Battery     int                  `json:"battery"`     // 电量
	DayMileage  float64              `json:"dayMileage"`  // 日里程
	OfflineTime types.MyUnixDateTime `json:"offlineTime"` // 离线时间
	Status      int                  `json:"status"`      // 设备状态(0:正常 1:故障)
	AdLocated   int                  `json:"adLocated"`   // 是否开启远程驾驶定位
}

// AlarmDetail 告警详情
type AlarmDetail struct {
	IMSI       string      `json:"imsi"`       // 设备IMSI
	AlarmCount int         `json:"alarmCount"` // 告警数量
	Alarms     []AlarmInfo `json:"alarms"`     // 告警列表
}

// DailyStats 每日统计数据
type DailyStats struct {
	DeviceAlarms []AlarmDetail `json:"deviceAlarms"` // 设备告警统计
}

// DeviceBaseStats 设备基础统计
type DeviceBaseStats struct {
	Total        int            `json:"total"`        // 设备总数
	OnlineCount  int            `json:"onlineCount"`  // 在线数量
	OfflineCount int            `json:"offlineCount"` // 离线数量
	TypeCount    map[string]int `json:"typeCount"`    // 各类型设备数量
	// DeviceTotal  int            `json:"deviceTotal"`  // 设备总数（兼容旧版）
}

// DeviceMileageStats 设备里程统计
type DeviceMileageStats struct {
	MonthMileage float64 `json:"monthMileage"` // 月度里程
	TodayMileage float64 `json:"todayMileage"` // 今日里程
}

// DeviceOperationStats 设备运营时长统计
type DeviceOperationStats struct {
	MonthDuration float64 `json:"monthDuration"` // 月度运营时长（小时）
	TodayDuration float64 `json:"todayDuration"` // 今日运营时长（小时）
}

// TaskStats 任务统计
type TaskStats struct {
	TaskTotal int `json:"taskTotal"` // 任务总数
	TaskDoing int `json:"taskDoing"` // 进行中任务数
	TaskDone  int `json:"taskDone"`  // 已完成任务数
	TaskUndo  int `json:"taskUndo"`  // 未完成任务数
}

// OfflineDistributionStats 离线分布统计
type OfflineDistributionStats struct {
	NeverOnline     int `json:"neverOnline"`     // 从未在线
	OfflineDay      int `json:"offlineDay"`      // 离线1天内
	OfflineDayThree int `json:"offlineDayThree"` // 离线1-3天
	OfflineDayFive  int `json:"offlineDayFive"`  // 离线3-5天
	OfflineDaySeven int `json:"offlineDaySeven"` // 离线5-7天
	OfflineDayLong  int `json:"offlineDayLong"`  // 离线7天以上
}

// VehicleTypeStats 车辆类型统计
type VehicleTypeStats struct {
	DeviceTotal int `json:"deviceTotal"` // 设备总数
	DeviceType0 int `json:"deviceType0"` // 类型0设备数量
	DeviceType1 int `json:"deviceType1"` // 类型1设备数量
	DeviceType2 int `json:"deviceType2"` // 类型2设备数量
	DeviceType3 int `json:"deviceType3"` // 类型3设备数量
	DeviceType4 int `json:"deviceType4"` // 类型4设备数量
}

// ModeStats 设备模式统计
type ModeStats struct {
	FaultState     int `json:"faultState"`     // 故障状态 (status=1)
	ManualControl  int `json:"manualControl"`  // 手动遥控状态 (mode=1)
	StandbyState   int `json:"standbyState"`   // 待机状态 (mode=2)
	AutoDriving    int `json:"autoDriving"`    // 自动驾驶状态 (mode=3)
	RemoteDriving  int `json:"remoteDriving"`  // 远程驾驶状态 (mode=4)
	SafetyTakeover int `json:"safetyTakeover"` // 安全接管状态 (mode=5)
}
