package service

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/models/pix"
	"ccapi/server/kafka"
	"ccapi/server/redis"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/beego/beego/v2/server/web/context"
	"sort"
	"strconv"
	"strings"
	"time"

	"ccapi/pkg/orm_helper"
	"github.com/IBM/sarama"
	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
)

type DeviceServiceInter interface {
	// SetNull 将被删除用户的车辆的uid置空
	SetNull(ids string) error
	// GetBindRemarkByUid 根据用户ID查询设备绑定备注
	GetBindRemarkByUid(uid int64) (string, error)
	// List 获取设备列表
	List(context *context.Context, query dto.DeviceListQuery, allId string, myId int64) ([]pix.Device, int64, error)
	// All 获取所有设备
	All(uid int, imsi string, online int, allId string, flag bool, _type int) ([]pix.Device, error)
	// CreateRespDeviceData 修正列出的数据
	CreateRespDeviceData(mondayTs int64, nowTs int64, device pix.Device) dto.RespDeviceForm
	// AddDevice 新增设备
	AddDevice(imei string, imsi string, name string, uid int, _type int, _real int, expireTime int, remoteDriving int,
		vinLogin int, push int, historyDays int, bindRemark string, mapId int) error
	// CheckImei 检查imsi是否重复
	CheckImei(imei string) bool
	// CheckImsi 检查imsi是否重复
	CheckImsi(imsi string) bool
	// Find 根据id获取设备
	Find(id int) (pix.Device, error)
	// FindAll 根据id获取设备
	FindAll(id []int64) ([]pix.Device, error)
	// Delete 根据id删除设备
	Delete(id int) error
	// Update 更新设备信息
	Update(where map[string]interface{}, col map[string]interface{}) error
	// SendMessage 通过kafka发送消息到tcp服务器
	SendMessage(data map[string]interface{}) error
	// GetPassword 根据imsi获取密码
	GetPassword(imsi string) (string, error)
	// FindTask 查找任务
	FindTask(id int64) (pix.Task, error)
	// FindDevice 查找设备
	FindDevice(id int64) ([]pix.Device, error)
	// Analysis 数据大屏
	Analysis(area string, level int, allId string, deviceId int, beginTime int, endTime int, idInt []int64) (map[string]interface{}, error)
	// GetVin 查询vin码
	GetVin(imsi string) (dto.Vin, error)
	// GetImsi 查询imsi
	GetImsi(vinCode string) (dto.Vin, error)
	// UpdateVin 更新vin码
	UpdateVin(imsi string, code string) error
	// DeleteMessage 删除过期提醒消息
	DeleteMessage(id int) error
	// GetPushConfig 获取推流配置
	GetPushConfig(imsi string) ([]pix.PushConfig, error)
	// UpdatePushConfig 修改推流配置
	UpdatePushConfig(imsi string, position int, open int, _type int) error
	// PushLog 获取推流记录
	PushLog(page int, limit int, position int, _type int, beginTime int, endTime int, allId string, imsi string) ([]pix.PushLog, error, int64)
	// FindByImsi 根据imsi获取设备信息
	FindByImsi(imsi string) (pix.Device, error)
}

type DeviceService struct {
}

func NewDeviceService() DeviceServiceInter {
	return &DeviceService{}
}

// SetNull 将被删除用户的车辆的uid置空
// param uid int64 用户id
// return    err   错误信息
func (s *DeviceService) SetNull(ids string) error {
	o := orm.NewOrm()
	_, err := o.Raw(fmt.Sprintf("update device set uid = 0 where uid in (%s)", ids)).Exec()
	return err
}

// List 获取设备列表
func (s *DeviceService) List(context *context.Context, query dto.DeviceListQuery, allId string, myId int64) ([]pix.Device, int64, error) {
	o := orm.NewOrm()



	result, err := orm_helper.NewQueryBuilder[pix.Device](o, context, "select * from device").
		InIf(myId != 1, "uid", allId).
		Like("imsi", query.Imsi).
		Like("imei", query.Imei).
		Like("name", query.Name).
		Like("city", query.City).
		Like("area", query.Area).
		Like("bind_remark", query.BindRemark).
		Eq("online", query.Online).
		Eq("is_vcu", query.IsVcu).
		Eq("type", query.Type).
		Eq("vin_login", query.VinLogin).
		EqIf(query.MapId != 0, "map_id", query.MapId).
		Page()

	if err != nil {
		return nil, 0, err
	}

	return result.List, result.Total, nil
}

// GetBindRemarkByUid 根据用户ID查询设备绑定备注
func (s *DeviceService) GetBindRemarkByUid(uid int64) (string, error) {
	o := orm.NewOrm()
	var device pix.Device

	// 查询uid等于指定ID的设备，并获取bind_remark字段
	query := o.QueryTable("device").Filter("uid", uid)
	err := query.One(&device, "bind_remark")

	if err != nil {
		// 处理查询错误或记录不存在的情况
		if err == orm.ErrNoRows {
			return "", nil // 没有找到记录时返回空字符串而非错误
		}
		return "", err
	}

	return device.BindRemark, nil
}

// All                       获取所有设备
// param online int          是否在线 0离线1在线
// param allId  string       所有子节点和自身的id
// return       []pix.Device 设备列表
// return       error        错误信息
func (s *DeviceService) All(uid int, imsi string, online int, allId string, flag bool, _type int) ([]pix.Device, error) {
	var (
		err error
		re  []pix.Device
	)

	// where := fmt.Sprintf(" uid in (%s)", allId)
	// if flag {
	// 	where = "1 = 1"
	// }
	//
	// if imsi != "" {
	// 	where += fmt.Sprintf(" and imsi like '%%%s%%'", imsi)
	// }
	// if online != -1 {
	// 	where += fmt.Sprintf(" and online = %d", online)
	// }
	// if uid != -1 {
	// 	where += fmt.Sprintf(" and uid = %d", uid)
	// }
	// if _type != -1 {
	// 	where += fmt.Sprintf(" and type = %d", _type)
	// }

	o := orm.NewOrm()
	// _, err = o.Raw(fmt.Sprintf("select device.*,user.name as username from device left join user on user.id = device.uid where %s order by online desc, id desc", where)).QueryRows(&re)
	re, err = orm_helper.NewQueryBuilder[pix.Device](o, nil, `select * from device`).Eq("uid", uid).
		Eq("online", online).Eq("type", _type).Like("imsi", imsi).Result()
	if err != nil {
		return nil, err
	}

	// 更新设备在线状态
	// now := time.Now().Unix()
	// for i := range re {
	// 	lastHeartbeatTs, err := common.GetRedisHeartbeatTs(re[i].Imsi)
	// 	if err == nil {
	// 		// 30s差值即离线
	// 		if (now - lastHeartbeatTs) > 30 {
	// 			re[i].Online = 0
	// 		} else {
	// 			re[i].Online = 1
	// 		}
	// 	} else {
	// 		re[i].Online = 0
	// 	}
	// }

	return re, nil
}

// CreateRespDeviceData              修正列出的数据
// param mondayTs int64              本周一时间戳
// param nowTs    int64              现在时间戳
// param device   pix.Device         设备信息
// return         dto.RespDeviceForm 修正后的设备信息
func (s *DeviceService) CreateRespDeviceData(mondayTs int64, nowTs int64, device pix.Device) dto.RespDeviceForm {
	var (
		re                     dto.RespDeviceForm
		err                    error
		currentOnlineTotalTime int64
	)
	// 初始化本周在线总时间为设备的已有在线时间
	currentWeekOnlineTotalTime := int64(device.TimeSpending)
	// 从 Redis 中获取设备的最后心跳数据
	do, err := redis.HGet(common.RedisLastHeartbeat, device.Imsi)
	if do != "" {
		// 如果获取到数据，尝试将其解析为 dto.RespDeviceForm 类型
		err = json.Unmarshal([]byte(do), &re)
		if err != nil {
			msg := fmt.Sprintf("%s心跳解析失败,%s", device.Imsi, err.Error())
			fmt.Println(msg)
		}
	}
	// 在线
	// if device.Online == 1 {
	// 在线, 则计算本次截止至当前的在线时间
	// thisLoginTs := common.ChangeTimeToInt(device.OnlineTime)
	// fixLoginTs := thisLoginTs
	// if fixLoginTs < mondayTs { // 若终端在本周一前上线的，则以本周一 0 点时间作为起点时间
	//	fixLoginTs = mondayTs
	// }
	// // 计算本周的在线总时间
	// currentWeekOnlineTotalTime += nowTs - fixLoginTs
	// // 计算当前在线总时间
	// currentOnlineTotalTime = nowTs - thisLoginTs
	// }

	var task pix.Task
	var taskChild pix.TaskChild

	o := orm.NewOrm()
	o.QueryTable("task").Filter("id", device.TaskId).One(&task)
	o.QueryTable("task_child").Filter("parent_id", device.TaskId).Filter("is_now", 1).One(&taskChild)

	re.ID = int64(device.Id)
	re.IMEI = device.Imei
	re.IMSI = device.Imsi
	re.IP = device.Ip
	re.Name = device.Name
	re.Vender = device.Vender
	re.HistoryDays = device.HistoryDays
	re.Model = device.Model
	re.ManageIp = device.ManageIp
	re.SoftwareVer = device.SoftwareVer
	re.HardwareVer = device.HardwareVer
	re.UserId = device.Uid
	re.Online = device.Online
	re.OnlineBigint = currentOnlineTotalTime
	re.TimeSpending = currentWeekOnlineTotalTime
	re.OnlineTime = device.OnlineTime
	re.OfflineTime = device.OfflineTime
	re.CreatedTime = device.CreatedTime
	re.UpdatedTime = device.UpdatedTime
	re.Advert = device.Advert
	re.AdvertType = device.AdvertType
	re.Type = device.Type
	re.Longitude = strconv.FormatFloat(re.Lng, 'f', 6, 64)
	re.Latitude = strconv.FormatFloat(re.Lat, 'f', 6, 64)
	re.MapId = device.MapId
	re.Task = task
	re.TaskChild = taskChild
	re.ExpireTime = device.ExpireTime
	re.RemoteDriving = device.RemoteDriving
	re.VinLogin = device.VinLogin
	re.Push = device.Push
	re.BindRemark = device.BindRemark
	return re
}

// AddDevice               新增设备
// param imei       string imei号
// param imsi       string imsi号
// param name       string 设备名
// param uid        int    所属用户id
// param _type      int    车辆类型
// param _real      int    是否为真实车辆
// param historyDays int   历史数据保留天数
// return           error  错误信息
func (s *DeviceService) AddDevice(imei string, imsi string, name string, uid int, _type int, _real int, expireTime int,
	remoteDriving int, vinLogin int, push int, historyDays int, bindRemark string, mapId int) error {
	var (
		err error
		now = time.Now().Format("2006-01-02 15:04:05")
	)

	// 如果 historyDays 为 0,设置默认值 180
	if historyDays == 0 {
		historyDays = 180
	}

	o := orm.NewOrm()

	_, err = o.Raw(fmt.Sprintf(`insert into device (imei, imsi, name, uid, type, created_time, updated_time, expire_time, remote_driving, vin_login, push, history_days,bind_remark,map_id) 
          values ('%s','%s','%s',%d, %d, '%s','%s', %d, %d, %d, %d, %d,'%s',%d)`, imei, imsi, name, uid, _type, now, now, expireTime, remoteDriving, vinLogin, push, historyDays, bindRemark, mapId)).Exec()

	return err
}

// CheckImei         检查imsi是否重复
// param imei string imei号
// return     bool   是否存在，0否1是
func (s *DeviceService) CheckImei(imei string) bool {
	o := orm.NewOrm()
	exist := o.QueryTable("device").Filter("imei", imei).Exist()

	return exist
}

// CheckImsi         检查imsi是否重复
// param imsi string imsi号
// return     bool   是否存在，0否1是
func (s *DeviceService) CheckImsi(imsi string) bool {
	o := orm.NewOrm()
	exist := o.QueryTable("device").Filter("imsi", imsi).Exist()

	return exist
}

// Find                根据id获取设备
// param id int        设备id
// return   pix.Device 设备信息
// return   error      错误信息
func (s *DeviceService) Find(id int) (pix.Device, error) {
	o := orm.NewOrm()
	device := pix.Device{Id: id}
	err := o.Read(&device)
	if err != nil {
		return pix.Device{}, err
	}
	return device, nil
}

// FindAll                根据id获取所有设备
// param id []int64        设备id
// return   [] pix.Device 设备信息
// return   error      错误信息
func (s *DeviceService) FindAll(id []int64) ([]pix.Device, error) {
	o := orm.NewOrm()
	var device []pix.Device
	_, err := o.QueryTable("device").Filter("id__in", id).All(&device)
	if err != nil {
		return []pix.Device{}, err
	}
	return device, nil
}

// Delete         根据id删除设备
// param id int   设备id
// return   error 错误信息
func (s *DeviceService) Delete(id int) error {
	o := orm.NewOrm()
	_, err := o.QueryTable("device").Filter("id", id).Delete()
	return err
}

// Update                             更新设备信息
// param where map[string]interface{} 条件
// param col   map[string]interface{} 要更新的字段
// return      error                  错误信息
func (s *DeviceService) Update(where map[string]interface{}, col map[string]interface{}) error {
	o := orm.NewOrm()
	table := o.QueryTable("device")
	var condition orm.QuerySeter

	if len(where) == 1 {
		condition = table.Filter("id", where["id"])
	} else {
		condition = table.Filter("id", where["id"]).Filter("uid", where["uid"])
	}

	_, err := condition.Update(col)
	return err
}

// DeleteMessage 删除过期提醒消息
func (s *DeviceService) DeleteMessage(id int) error {
	o := orm.NewOrm()
	_, err := o.QueryTable("message").Filter("device_id", id).Delete()

	return err
}

// SendMessage                       通过kafka发送消息到tcp服务器
// param data map[string]interface{} 消息
// return     error                  错误信息
func (s *DeviceService) SendMessage(data map[string]interface{}) error {
	topic, _ := beego.AppConfig.String("kafka_topic")
	msg := &sarama.ProducerMessage{}
	msg.Topic = topic

	dataStr, _ := json.Marshal(data)
	msg.Value = sarama.StringEncoder(dataStr)

	fmt.Printf("🚀 发送Kafka消息到topic[%s]: %s\n", topic, string(dataStr))

	_, _, err := kafka.Producer().SendMessage(msg)
	if err != nil {
		fmt.Printf("❌ Kafka消息发送失败: %v\n", err)
	} else {
		fmt.Printf("✅ Kafka消息发送成功\n")
	}
	return err
}

// GetPassword 根据imsi获取密码
func (s *DeviceService) GetPassword(imsi string) (string, error) {
	o := orm.NewOrm()
	var device pix.Device
	err := o.QueryTable("device").Filter("imsi", imsi).One(&device)
	if err != nil {
		return "", err
	}

	return device.Password, nil
}

// FindByImsi 根据imsi获取设备信息
func (s *DeviceService) FindByImsi(imsi string) (pix.Device, error) {
	o := orm.NewOrm()
	var device pix.Device
	err := o.QueryTable("device").Filter("imsi", imsi).One(&device)
	if err != nil {
		return pix.Device{}, err
	}
	return device, nil
}

// FindTask 查找任务
func (s *DeviceService) FindTask(id int64) (pix.Task, error) {
	o := orm.NewOrm()
	var task pix.Task
	err := o.QueryTable("task").Filter("id", id).One(&task)

	return task, err
}

// FindDevice 查找设备
func (s *DeviceService) FindDevice(id int64) ([]pix.Device, error) {
	o := orm.NewOrm()
	var device []pix.Device
	_, err := o.QueryTable("device").Filter("task_id", id).All(&device)

	return device, err
}

// Analysis 数据大屏
func (s *DeviceService) Analysis(area string, level int, allId string, deviceId int, beginTime int, endTime int, idInt []int64) (map[string]interface{}, error) {
	o := orm.NewOrm()
	var (
		err         error
		re          dto.RespDeviceForm
		do          string
		devices     []dto.Device
		tasks       []pix.Task
		taskLogs    []pix.TaskLog
		china       []pix.China
		message     []pix.Message
		deviceTotal = 0
		online      = 0
		offline     = 0

		mileageTotal      = 0
		mileageMonthTotal = 0
		mileageDayTotal   = 0
		deviceType0       = 0
		deviceType1       = 0
		deviceType2       = 0
		deviceType3       = 0
		deviceType4       = 0

		taskIdsStr     []string
		taskTotal      = 0
		taskMonthTotal = 0
		taskDoing      = 0
		taskDone       = 0
		taskUndo       = 0
		taskMonthDoing = 0
		taskMonthDone  = 0
		taskMonthUndo  = 0
		taskMonth      = 0
		taskDay        = 0

		neverOnline     = 0
		offlineDay      = 0
		offlineDayThree = 0
		offlineDayFive  = 0
		offlineDaySeven = 0
		offlineDayLong  = 0

		now = time.Now().Unix()
		// nowHour   = time.Now().Hour()
		// firstHour = common.GetZeroTime3(time.Now(), nowHour)
		firstDay = common.GetZeroTime2(time.Now())

		taskInfo      []map[string]interface{}
		city          []map[string]interface{}
		notification  []map[string]interface{}
		alarm         []map[string]interface{}
		messageList   []map[string]interface{}
		users         []dto.AnalysisUser
		dayMileage    []dto.DeviceMileage
		monthMileage  []dto.DeviceMileage
		deviceMessage []dto.DeviceMessage
	)

	where := fmt.Sprintf(" uid in (%s)", allId)
	where2 := fmt.Sprintf(" user_id in (%s)", allId)

	if beginTime != 0 {
		where2 += fmt.Sprintf(" and message_time >= %d", beginTime)
	}

	if endTime != 0 {
		where2 += fmt.Sprintf(" and message_time <= %d", endTime)
	}

	if deviceId != 0 {
		where2 += fmt.Sprintf(" and device_id = %d", deviceId)
	}

	name := "province"
	if level == 2 {
		name = "city"
	} else if level == 3 {
		name = "area"
	}

	if area != "" {
		where += fmt.Sprintf(" and %s = '%s'", name, area)
	}
	_, err = o.Raw(fmt.Sprintf("select device.*,user.name as username from device left join user on user.id = device.uid where %s", where)).QueryRows(&devices)

	_, err = o.Raw(fmt.Sprintf("select * from task where uid in (%s)", allId)).QueryRows(&tasks)

	_, err = o.Raw(fmt.Sprintf("select * from message where %s and created_time >= %d", where2, now-30*86400)).QueryRows(&message)

	_, err = o.Raw("select * from china where pid = 0 and id > 0").QueryRows(&china)

	if err != nil {
		return nil, err
	}

	if len(tasks) > 0 {
		for _, v := range tasks {
			taskIdsStr = append(taskIdsStr, fmt.Sprintf("%d", v.Id))
		}

		allTaskId := strings.Join(taskIdsStr, ",")
		_, err = o.Raw(fmt.Sprintf("select * from task_log where task_id in(%s) order by id desc;", allTaskId)).QueryRows(&taskLogs)
		if err != nil {
			return nil, err
		}
	}

	for d, u := range idInt {
		user := pix.User{Id: u}
		err = o.Read(&user)
		if err != nil {
			return nil, err
		}

		users = append(users, dto.AnalysisUser{
			No:       d + 1,
			Uid:      u,
			Username: user.Username,
		})
	}

	taskTotal = len(taskLogs)

	for _, taskLog := range taskLogs {
		if taskLog.Status == 2 {
			taskDoing++
		} else if taskLog.Status == 3 {
			taskDone++
		}

		if taskLog.CreatedTime >= now-30*86400 {
			taskMonth++
			taskMonthTotal++
			if taskLog.Status == 2 {
				taskMonthDoing++
			} else if taskLog.Status == 3 {
				taskMonthDone++
			}
		}

		if taskLog.CreatedTime >= firstDay.Unix() {
			taskDay++
		}
	}

	deviceTotal = len(devices)

	for k, device := range devices {
		do, err = redis.HGet(common.RedisLastHeartbeat, device.Imsi)
		if do != "" {
			json.Unmarshal([]byte(do), &re)
		}

		var totalHeart pix.RecordSignal
		var dayHeart pix.RecordSignal
		var monthHeart pix.RecordSignal

		o.Raw(fmt.Sprintf("select * from record_signal where imsi = '%s' order by id desc limit 1", device.Imsi)).QueryRow(&totalHeart)
		o.Raw(fmt.Sprintf("select * from record_signal where imsi = '%s' and created_time > %d and tm != 0 limit 1", device.Imsi, now-30*86400)).QueryRow(&monthHeart)
		o.Raw(fmt.Sprintf("select * from record_signal where imsi = '%s' and created_time > %d limit 1", device.Imsi, firstDay.Unix())).QueryRow(&dayHeart)

		if monthHeart.Tm == 0 {
			mileageMonthTotal += monthHeart.Tm
		} else {
			mileageMonthTotal += int(re.Tm) - monthHeart.Tm
		}

		if dayHeart.Tm == 0 {
			mileageDayTotal += dayHeart.Tm
		} else {
			mileageDayTotal += int(re.Tm) - dayHeart.Tm
		}

		mileageTotal += totalHeart.Tm

		dayMileage = append(dayMileage, dto.DeviceMileage{
			Id:       device.Id,
			Imsi:     device.Imsi,
			Name:     device.Name,
			Uid:      device.Uid,
			Username: device.Username,
			Mileage:  int(re.Tm) - dayHeart.Tm,
		})

		monthMileage = append(monthMileage, dto.DeviceMileage{
			Id:       device.Id,
			Imsi:     device.Imsi,
			Name:     device.Name,
			Uid:      device.Uid,
			Username: device.Username,
			Mileage:  int(re.Tm) - monthHeart.Tm,
		})

		if device.Online == 1 {
			online++
		} else {
			offline++
		}

		if device.Type == 0 {
			deviceType0++
		} else if device.Type == 1 {
			deviceType1++
		} else if device.Type == 2 {
			deviceType2++
		} else if device.Type == 3 {
			deviceType3++
		} else if device.Type == 4 {
			deviceType4++
		}

		if device.OnlineTime == "" {
			neverOnline++
		}

		if device.OfflineTime != "" && device.Online == 0 {
			timeInt := common.ChangeTimeToInt(device.OfflineTime)
			timeArea := now - timeInt
			if timeArea < 86400 {
				offlineDay++
			} else if timeArea < 86400*3 && timeArea >= 86400 {
				offlineDayThree++
			} else if timeArea < 86400*5 && timeArea >= 86400*3 {
				offlineDayFive++
			} else if timeArea < 86400*7 && timeArea >= 86400*5 {
				offlineDaySeven++
			} else if timeArea >= 86400*7 {
				offlineDayLong++
			}
		}

		taskDoneDevice := 0
		taskTotalDevice := 0
		for _, taskLog := range taskLogs {
			if taskLog.Status == 3 && taskLog.Imsi == device.Imsi {
				taskDoneDevice++
			}
			if taskLog.Imsi == device.Imsi {
				taskTotalDevice++
			}
		}

		taskInfo = append(taskInfo, map[string]interface{}{
			"no":          k + 1,
			"device_name": device.Name,
			"task_done":   taskDoneDevice,
			"task_undo":   taskTotalDevice - taskDoneDevice,
		})

		for r, u := range users {
			if int64(device.Uid) == u.Uid {
				users[r].Total++
				if device.Online == 1 {
					users[r].Online++
				} else {
					users[r].Offline++
				}
			}
		}

		messageNum := 0
		for _, m := range message {
			if device.Uid == m.DeviceId {
				messageNum += 1
			}
		}

		deviceMessage = append(deviceMessage, dto.DeviceMessage{
			Id:       device.Id,
			Imsi:     device.Imsi,
			Name:     device.Name,
			Uid:      device.Uid,
			Username: device.Username,
			Num:      messageNum,
		})
	}

	for _, province := range china {
		num := 0
		messageNum := 0
		for _, device := range devices {
			if device.Province == province.Name {
				num++
			}

			for _, e := range message {
				if device.Id == e.DeviceId {
					messageNum++
				}
			}
		}
		city = append(city, map[string]interface{}{
			"position":    province.Name,
			"number":      num,
			"message_num": messageNum,
		})
	}

	taskUndo = taskTotal - taskDoing - taskDone
	taskMonthUndo = taskMonthTotal - taskMonthDoing - taskMonthDone

	for i := 9; i >= 0; i-- {
		var countHeart int64
		var countAlarm int64

		lastDay := firstDay.Unix() - int64(i*86400)

		o.Raw(fmt.Sprintf("select count(*) from record_signal where uid in (%s) and created_time >= %d and created_time < %d", allId, lastDay, lastDay+86400)).QueryRow(&countHeart)
		o.Raw(fmt.Sprintf("select count(*) from message where user_id in (%s) and created_time >= %d and created_time < %d", allId, lastDay, lastDay+86400)).QueryRow(&countAlarm)

		notification = append(notification, map[string]interface{}{
			"time":  fmt.Sprintf("%d", lastDay),
			"heart": countHeart,
			"alarm": countAlarm,
		})
	}

	sort.Slice(dayMileage, func(i, j int) bool {
		return dayMileage[i].Mileage > dayMileage[j].Mileage
	})

	sort.Slice(monthMileage, func(i, j int) bool {
		return monthMileage[i].Mileage > monthMileage[j].Mileage
	})

	sort.Slice(deviceMessage, func(i, j int) bool {
		return deviceMessage[i].Num > deviceMessage[j].Num
	})

	if len(dayMileage) > 10 {
		dayMileage = dayMileage[:10]
	}

	if len(monthMileage) > 10 {
		monthMileage = monthMileage[:10]
	}

	returnData := map[string]interface{}{
		"device_total":        deviceTotal,
		"online":              online,
		"offline":             offline,
		"task_total":          taskTotal,
		"task_month_total":    taskMonthTotal,
		"mileage_month_total": mileageMonthTotal,
		"mileage_total":       mileageTotal,
		"mileage_day_total":   mileageDayTotal,
		"device_type_0":       deviceType0,
		"device_type_1":       deviceType1,
		"device_type_2":       deviceType2,
		"device_type_3":       deviceType3,
		"device_type_4":       deviceType4,
		"task_doing":          taskDoing,
		"task_done":           taskDone,
		"task_undo":           taskUndo,
		"task_month":          taskMonth,
		"task_month_doing":    taskMonthDoing,
		"task_month_done":     taskMonthDone,
		"task_month_undo":     taskMonthUndo,
		"day_mileage":         dayMileage,
		"month_mileage":       monthMileage,
		"device_message":      deviceMessage,
		"task_day":            taskDay,
		"never_online":        neverOnline,
		"offline_day":         offlineDay,
		"offline_day_three":   offlineDayThree,
		"offline_day_five":    offlineDayFive,
		"offline_day_seven":   offlineDaySeven,
		"offline_day_long":    offlineDayLong,
		"task_info":           taskInfo,
		"area":                city,
		"message":             notification,
		"alarm":               alarm,
		"users":               users,
		"message_list":        messageList,
		"level":               area,
	}

	return returnData, nil
}

// GetVin 查询vin码
func (s *DeviceService) GetVin(imsi string) (dto.Vin, error) {
	o := orm.NewOrm()
	var vin dto.Vin
	var device pix.Device
	err := o.QueryTable("device").Filter("imsi", imsi).One(&device)

	vin.Imsi = device.Imsi
	vin.VinLogin = device.VinLogin
	vin.VinCode = device.VinCode
	vin.RemoteDriving = device.RemoteDriving
	return vin, err
}

// GetImsi 查询imsi
func (s *DeviceService) GetImsi(vinCode string) (dto.Vin, error) {
	o := orm.NewOrm()
	var vin dto.Vin
	var device pix.Device
	err := o.QueryTable("device").Filter("vin_code", vinCode).One(&device)

	vin.Imsi = device.Imsi
	vin.VinLogin = device.VinLogin
	vin.VinCode = device.VinCode
	vin.RemoteDriving = device.RemoteDriving
	return vin, err
}

// UpdateVin 更新vin码
func (s *DeviceService) UpdateVin(imsi string, code string) error {
	o := orm.NewOrm()

	exist := o.QueryTable("device").Exclude("imsi", imsi).Filter("vin_code", code).Exist()
	if exist {
		return errors.New("该vin码已被使用")
	}

	_, err := o.QueryTable("device").Filter("imsi", imsi).Update(orm.Params{
		"vin_code":     code,
		"updated_time": time.Now().Format("2006-01-02 15:04:05"),
	})

	return err
}

// GetPushConfig 获取推流配置
func (s *DeviceService) GetPushConfig(imsi string) ([]pix.PushConfig, error) {
	o := orm.NewOrm()
	var data []pix.PushConfig
	o.QueryTable("push_config").Filter("imsi", imsi).All(&data)
	if len(data) == 0 {
		now := time.Now().Unix()
		var newDataArr []pix.PushConfig
		for i := 0; i < 7; i++ {
			var newData pix.PushConfig
			newData.Imsi = imsi
			newData.Position = i + 1
			newData.Status = 0
			newData.Open = 0
			newData.PushStatus = 0
			newData.Type = 0
			newData.Url = ""
			newData.CreatedTime = now
			newData.UpdatedTime = now
			_, err := o.Insert(&newData)
			if err != nil {
				return nil, err
			}
			newDataArr = append(newDataArr, newData)
		}

		return newDataArr, nil
	} else {
		return data, nil
	}
}

// UpdatePushConfig 修改推流配置
func (s *DeviceService) UpdatePushConfig(imsi string, position int, open int, _type int) error {
	o := orm.NewOrm()
	_, err := o.QueryTable("push_config").Filter("imsi", imsi).Filter("position", position).Update(orm.Params{
		"open":         open,
		"type":         _type,
		"updated_time": time.Now().Unix(),
	})
	return err
}

// PushLog 获取推流记录
func (s *DeviceService) PushLog(page int, limit int, position int, _type int, beginTime int, endTime int, allId string, imsi string) ([]pix.PushLog, error, int64) {
	o := orm.NewOrm()
	var pushLog []pix.PushLog
	var count int64
	where := fmt.Sprintf(" device.uid in (%s)", allId)
	if position != -1 {
		where += fmt.Sprintf(" and push_log.position = %d", position)
	}

	if _type != -1 {
		where += fmt.Sprintf(" and push_log.type = %d", _type)
	}

	if imsi != "" {
		where += fmt.Sprintf(" and push_log.imsi like '%%%s%%'", imsi)
	}

	if beginTime != 0 {
		where += fmt.Sprintf(" and push_log.begin_time >= %d", beginTime)
	}

	if endTime != 0 {
		where += fmt.Sprintf(" and push_log.end_time <= %d", endTime)
	}

	_, err := o.Raw(fmt.Sprintf(`
		select device.uid,
		push_log.* from push_log 
		left join device on push_log.imsi = device.imsi 
		where %s order by id desc limit %d offset %d`, where, limit, (page-1)*limit)).QueryRows(&pushLog)

	if err != nil {
		return pushLog, err, count
	}

	err = o.Raw(fmt.Sprintf(`
		select count(*) from push_log 
		left join device on push_log.imsi = device.imsi 
		where %s`, where)).QueryRow(&count)

	return pushLog, err, count
}
