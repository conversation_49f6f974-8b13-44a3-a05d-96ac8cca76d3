package service

import (
	"fmt"
	"os"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"

	"ccapi/models/pix"
)

// GoViewBackgroundService GoView背景管理服务
type GoViewBackgroundService struct{}

// AddBackground 添加背景记录
func (s *GoViewBackgroundService) AddBackground(projectID, fileName, fileURL, createUserID string, fileSize int64) (*pix.GoViewProjectBackground, error) {
	o := orm.NewOrm()
	
	// 先将该项目的其他背景设置为非活跃状态
	_, err := o.Raw("UPDATE goview_project_backgrounds SET is_active = 0 WHERE project_id = ?", projectID).Exec()
	if err != nil {
		logs.Error("更新背景活跃状态失败:", err)
		return nil, fmt.Errorf("更新背景活跃状态失败: %v", err)
	}
	
	// 创建新的背景记录
	background := &pix.GoViewProjectBackground{
		ProjectID:    projectID,
		FileName:     fileName,
		FileURL:      fileURL,
		FileSize:     fileSize,
		IsActive:     true, // 新上传的背景默认为活跃状态
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
		CreateUserID: createUserID,
	}
	
	_, err = o.Insert(background)
	if err != nil {
		logs.Error("插入背景记录失败:", err)
		return nil, fmt.Errorf("插入背景记录失败: %v", err)
	}
	
	return background, nil
}

// GetProjectBackgrounds 获取项目背景列表
func (s *GoViewBackgroundService) GetProjectBackgrounds(projectID string) ([]*pix.GoViewProjectBackground, error) {
	o := orm.NewOrm()
	var backgrounds []*pix.GoViewProjectBackground
	
	_, err := o.QueryTable("goview_project_backgrounds").
		Filter("project_id", projectID).
		OrderBy("-create_time").
		All(&backgrounds)
	
	if err != nil {
		logs.Error("查询项目背景列表失败:", err)
		return nil, fmt.Errorf("查询项目背景列表失败: %v", err)
	}
	
	return backgrounds, nil
}

// DeleteBackground 删除背景
func (s *GoViewBackgroundService) DeleteBackground(projectID string, backgroundID int64) error {
	o := orm.NewOrm()
	
	// 先查询背景记录
	background := &pix.GoViewProjectBackground{}
	err := o.QueryTable("goview_project_backgrounds").
		Filter("id", backgroundID).
		Filter("project_id", projectID).
		One(background)
	
	if err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("背景记录不存在")
		}
		logs.Error("查询背景记录失败:", err)
		return fmt.Errorf("查询背景记录失败: %v", err)
	}
	
	// 删除物理文件
	filePath := fmt.Sprintf("uploads/backgrounds/%s", background.FileName)
	if err := os.Remove(filePath); err != nil {
		logs.Warning("删除背景文件失败:", err)
		// 文件删除失败不影响数据库记录删除
	}
	
	// 删除数据库记录
	_, err = o.Delete(background)
	if err != nil {
		logs.Error("删除背景记录失败:", err)
		return fmt.Errorf("删除背景记录失败: %v", err)
	}
	
	return nil
}

// SetActiveBackground 设置活跃背景
func (s *GoViewBackgroundService) SetActiveBackground(projectID string, backgroundID int64) error {
	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	defer func() {
		if err != nil {
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				logs.Error("回滚事务失败:", rollbackErr)
			}
		} else {
			commitErr := tx.Commit()
			if commitErr != nil {
				logs.Error("提交事务失败:", commitErr)
				err = fmt.Errorf("提交事务失败: %v", commitErr)
			}
		}
	}()

	// 先将该项目的所有背景设置为非活跃状态
	_, err = tx.Raw("UPDATE goview_project_backgrounds SET is_active = 0 WHERE project_id = ?", projectID).Exec()
	if err != nil {
		logs.Error("更新背景活跃状态失败:", err)
		return fmt.Errorf("更新背景活跃状态失败: %v", err)
	}

	// 设置指定背景为活跃状态
	_, err = tx.Raw("UPDATE goview_project_backgrounds SET is_active = 1 WHERE id = ? AND project_id = ?", backgroundID, projectID).Exec()
	if err != nil {
		logs.Error("设置活跃背景失败:", err)
		return fmt.Errorf("设置活跃背景失败: %v", err)
	}

	return nil
}

// GetActiveBackground 获取项目的活跃背景
func (s *GoViewBackgroundService) GetActiveBackground(projectID string) (*pix.GoViewProjectBackground, error) {
	o := orm.NewOrm()
	background := &pix.GoViewProjectBackground{}
	
	err := o.QueryTable("goview_project_backgrounds").
		Filter("project_id", projectID).
		Filter("is_active", true).
		One(background)
	
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有活跃背景
		}
		logs.Error("查询活跃背景失败:", err)
		return nil, fmt.Errorf("查询活跃背景失败: %v", err)
	}
	
	return background, nil
}
