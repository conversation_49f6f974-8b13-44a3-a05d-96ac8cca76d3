package service

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/core/logs"
	"ccapi/models/pix"
)

// GoViewImageService GoView图片管理服务
type GoViewImageService struct{}

// AddImage 添加图片记录
func (s *GoViewImageService) AddImage(projectID, fileName, fileURL, createUserID string, fileSize int64, tags []string) (*pix.GoViewProjectImage, error) {
	o := orm.NewOrm()
	
	// 如果包含background标签，先将该项目的其他背景设置为非活跃状态
	isBackground := false
	for _, tag := range tags {
		if tag == "background" {
			isBackground = true
			break
		}
	}
	
	if isBackground {
		_, err := o.Raw("UPDATE goview_project_images SET is_active = 0 WHERE project_id = ? AND JSON_CONTAINS(tags, '\"background\"')", projectID).Exec()
		if err != nil {
			logs.Error("更新背景活跃状态失败:", err)
			return nil, fmt.Errorf("更新背景活跃状态失败: %v", err)
		}
	}
	
	// 将标签数组转换为JSON字符串
	tagsJSON, err := json.Marshal(tags)
	if err != nil {
		logs.Error("标签JSON序列化失败:", err)
		return nil, fmt.Errorf("标签JSON序列化失败: %v", err)
	}
	
	// 创建新的图片记录
	image := &pix.GoViewProjectImage{
		ProjectID:    projectID,
		FileName:     fileName,
		FileURL:      fileURL,
		FileSize:     fileSize,
		Tags:         string(tagsJSON),
		IsActive:     isBackground, // 背景图片默认为活跃状态
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
		CreateUserID: createUserID,
	}
	
	// 插入数据库
	id, err := o.Insert(image)
	if err != nil {
		logs.Error("插入图片记录失败:", err)
		return nil, fmt.Errorf("插入图片记录失败: %v", err)
	}
	
	image.ID = id
	logs.Info("图片记录添加成功, ID:", id)
	
	return image, nil
}

// GetProjectImages 获取项目图片列表
func (s *GoViewImageService) GetProjectImages(projectID string, tags []string) ([]*pix.GoViewProjectImage, error) {
	o := orm.NewOrm()
	var images []*pix.GoViewProjectImage
	
	qs := o.QueryTable("goview_project_images").Filter("project_id", projectID)
	
	// 如果指定了标签过滤
	if len(tags) > 0 {
		for _, tag := range tags {
			if tag != "all" { // "all"标签表示不过滤
				qs = qs.Filter("tags__contains", fmt.Sprintf("\"%s\"", tag))
			}
		}
	}
	
	_, err := qs.OrderBy("-create_time").All(&images)
	if err != nil {
		logs.Error("查询项目图片列表失败:", err)
		return nil, fmt.Errorf("查询项目图片列表失败: %v", err)
	}
	
	return images, nil
}

// DeleteImage 删除图片
func (s *GoViewImageService) DeleteImage(projectID string, imageID int64) error {
	o := orm.NewOrm()
	
	// 验证图片是否属于该项目
	var image pix.GoViewProjectImage
	err := o.QueryTable("goview_project_images").
		Filter("id", imageID).
		Filter("project_id", projectID).
		One(&image)
	
	if err != nil {
		if err == orm.ErrNoRows {
			return fmt.Errorf("图片不存在或不属于该项目")
		}
		logs.Error("查询图片失败:", err)
		return fmt.Errorf("查询图片失败: %v", err)
	}
	
	// 删除图片记录
	_, err = o.Delete(&image)
	if err != nil {
		logs.Error("删除图片记录失败:", err)
		return fmt.Errorf("删除图片记录失败: %v", err)
	}
	
	logs.Info("图片删除成功, ID:", imageID)
	return nil
}

// SetActiveImage 设置活跃图片（主要用于背景）
func (s *GoViewImageService) SetActiveImage(projectID string, imageID int64) error {
	o := orm.NewOrm()

	// 开启事务
	tx, err := o.Begin()
	if err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}

	defer func() {
		if err != nil {
			rollbackErr := tx.Rollback()
			if rollbackErr != nil {
				logs.Error("回滚事务失败:", rollbackErr)
			}
		} else {
			commitErr := tx.Commit()
			if commitErr != nil {
				logs.Error("提交事务失败:", commitErr)
				err = fmt.Errorf("提交事务失败: %v", commitErr)
			}
		}
	}()

	// 先将该项目的所有背景图片设置为非活跃状态
	_, err = tx.Raw("UPDATE goview_project_images SET is_active = 0 WHERE project_id = ? AND JSON_CONTAINS(tags, '\"background\"')", projectID).Exec()
	if err != nil {
		logs.Error("更新图片活跃状态失败:", err)
		return fmt.Errorf("更新图片活跃状态失败: %v", err)
	}

	// 设置指定图片为活跃状态
	_, err = tx.Raw("UPDATE goview_project_images SET is_active = 1 WHERE id = ? AND project_id = ?", imageID, projectID).Exec()
	if err != nil {
		logs.Error("设置活跃图片失败:", err)
		return fmt.Errorf("设置活跃图片失败: %v", err)
	}

	return nil
}

// GetActiveImage 获取项目的活跃图片（主要用于背景）
func (s *GoViewImageService) GetActiveImage(projectID string, tag string) (*pix.GoViewProjectImage, error) {
	o := orm.NewOrm()
	image := &pix.GoViewProjectImage{}
	
	qs := o.QueryTable("goview_project_images").
		Filter("project_id", projectID).
		Filter("is_active", true)
	
	// 如果指定了标签，添加标签过滤
	if tag != "" {
		qs = qs.Filter("tags__contains", fmt.Sprintf("\"%s\"", tag))
	}
	
	err := qs.One(image)
	
	if err != nil {
		if err == orm.ErrNoRows {
			return nil, nil // 没有活跃图片
		}
		logs.Error("查询活跃图片失败:", err)
		return nil, fmt.Errorf("查询活跃图片失败: %v", err)
	}
	
	return image, nil
}

// ParseTags 解析标签JSON字符串
func (s *GoViewImageService) ParseTags(tagsJSON string) ([]string, error) {
	var tags []string
	if tagsJSON == "" {
		return tags, nil
	}
	
	err := json.Unmarshal([]byte(tagsJSON), &tags)
	if err != nil {
		return nil, fmt.Errorf("解析标签失败: %v", err)
	}
	
	return tags, nil
}

// HasTag 检查图片是否包含指定标签
func (s *GoViewImageService) HasTag(image *pix.GoViewProjectImage, tag string) bool {
	tags, err := s.ParseTags(image.Tags)
	if err != nil {
		return false
	}
	
	for _, t := range tags {
		if t == tag {
			return true
		}
	}
	
	return false
}

// MigrateFromBackgrounds 从背景表迁移数据到图片表
func (s *GoViewImageService) MigrateFromBackgrounds() error {
	o := orm.NewOrm()
	
	// 查询所有背景数据
	var backgrounds []*pix.GoViewProjectBackground
	_, err := o.QueryTable("goview_project_backgrounds").All(&backgrounds)
	if err != nil {
		logs.Error("查询背景数据失败:", err)
		return fmt.Errorf("查询背景数据失败: %v", err)
	}
	
	// 迁移每条背景数据
	for _, bg := range backgrounds {
		// 检查是否已经迁移过
		var existingImage pix.GoViewProjectImage
		err := o.QueryTable("goview_project_images").
			Filter("file_url", bg.FileURL).
			One(&existingImage)
		
		if err == nil {
			// 已存在，跳过
			continue
		}
		
		if err != orm.ErrNoRows {
			logs.Error("检查图片是否存在失败:", err)
			continue
		}
		
		// 创建图片记录
		tags := []string{"background"}
		_, err = s.AddImage(bg.ProjectID, bg.FileName, bg.FileURL, bg.CreateUserID, bg.FileSize, tags)
		if err != nil {
			logs.Error("迁移背景数据失败:", err)
			continue
		}
		
		logs.Info("成功迁移背景数据:", bg.FileName)
	}
	
	return nil
}
