package common

import (
	"fmt"
	"strings"

	beego "github.com/beego/beego/v2/server/web"
)

// ServerConfig 服务器配置
type ServerConfig struct {
	BaseURL           string // 服务器基础地址
	UploadURLPrefix   string // 上传文件URL前缀
	GoViewDataOriginURL string // GoView数据源地址
}

// GetServerConfig 获取服务器配置
func GetServerConfig() *ServerConfig {
	config := &ServerConfig{}
	
	// 读取配置，如果读取失败则使用默认值
	config.BaseURL = beego.AppConfig.DefaultString("server_base_url", "http://localhost:8086")
	config.UploadURLPrefix = beego.AppConfig.DefaultString("upload_url_prefix", "/uploads")
	config.GoViewDataOriginURL = beego.AppConfig.DefaultString("goview_data_origin_url", "http://127.0.0.1:8086")
	
	// 确保BaseURL不以/结尾
	config.BaseURL = strings.TrimSuffix(config.BaseURL, "/")
	
	// 确保UploadURLPrefix以/开头
	if !strings.HasPrefix(config.UploadURLPrefix, "/") {
		config.UploadURLPrefix = "/" + config.UploadURLPrefix
	}
	
	return config
}

// GetUploadURL 获取上传文件的完整URL
func (c *ServerConfig) GetUploadURL(filePath string) string {
	// 移除filePath开头的/或./
	filePath = strings.TrimPrefix(filePath, "/")
	filePath = strings.TrimPrefix(filePath, "./")
	
	return fmt.Sprintf("%s/%s", c.BaseURL, filePath)
}

// GetBucketURL 获取文件存储桶URL（兼容GoView的bucketURL字段）
func (c *ServerConfig) GetBucketURL() string {
	return fmt.Sprintf("%s%s/", c.BaseURL, c.UploadURLPrefix)
}

// 全局配置实例
var serverConfig *ServerConfig

// init 初始化配置
func init() {
	serverConfig = GetServerConfig()
}

// GetGlobalServerConfig 获取全局服务器配置实例
func GetGlobalServerConfig() *ServerConfig {
	if serverConfig == nil {
		serverConfig = GetServerConfig()
	}
	return serverConfig
}
