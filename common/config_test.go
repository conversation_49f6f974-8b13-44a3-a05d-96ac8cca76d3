package common

import (
	"testing"
	beego "github.com/beego/beego/v2/server/web"
)

func TestServerConfig(t *testing.T) {
	// 设置测试配置
	beego.AppConfig.Set("server_base_url", "http://test.example.com")
	beego.AppConfig.Set("upload_url_prefix", "/files")
	beego.AppConfig.Set("goview_data_origin_url", "http://data.example.com")
	
	config := GetServerConfig()
	
	// 测试基础配置读取
	if config.BaseURL != "http://test.example.com" {
		t.Errorf("Expected BaseURL to be 'http://test.example.com', got '%s'", config.BaseURL)
	}
	
	if config.UploadURLPrefix != "/files" {
		t.Errorf("Expected UploadURLPrefix to be '/files', got '%s'", config.UploadURLPrefix)
	}
	
	if config.GoViewDataOriginURL != "http://data.example.com" {
		t.Errorf("Expected GoViewDataOriginURL to be 'http://data.example.com', got '%s'", config.GoViewDataOriginURL)
	}
	
	// 测试URL生成
	bucketURL := config.GetBucketURL()
	expectedBucketURL := "http://test.example.com/files/"
	if bucketURL != expectedBucketURL {
		t.Errorf("Expected BucketURL to be '%s', got '%s'", expectedBucketURL, bucketURL)
	}
	
	// 测试文件URL生成
	fileURL := config.GetUploadURL("uploads/test.png")
	expectedFileURL := "http://test.example.com/uploads/test.png"
	if fileURL != expectedFileURL {
		t.Errorf("Expected file URL to be '%s', got '%s'", expectedFileURL, fileURL)
	}
	
	// 测试路径处理
	fileURL2 := config.GetUploadURL("/uploads/test.jpg")
	expectedFileURL2 := "http://test.example.com/uploads/test.jpg"
	if fileURL2 != expectedFileURL2 {
		t.Errorf("Expected file URL to be '%s', got '%s'", expectedFileURL2, fileURL2)
	}
}

func TestDefaultConfig(t *testing.T) {
	// 清除配置，测试默认值
	beego.AppConfig.Set("server_base_url", "")
	beego.AppConfig.Set("upload_url_prefix", "")
	beego.AppConfig.Set("goview_data_origin_url", "")
	
	config := GetServerConfig()
	
	// 测试默认值
	if config.BaseURL != "http://localhost:8086" {
		t.Errorf("Expected default BaseURL to be 'http://localhost:8086', got '%s'", config.BaseURL)
	}
	
	if config.UploadURLPrefix != "/uploads" {
		t.Errorf("Expected default UploadURLPrefix to be '/uploads', got '%s'", config.UploadURLPrefix)
	}
	
	if config.GoViewDataOriginURL != "http://127.0.0.1:8086" {
		t.Errorf("Expected default GoViewDataOriginURL to be 'http://127.0.0.1:8086', got '%s'", config.GoViewDataOriginURL)
	}
}
