import { fileURLToPath, URL } from 'node:url'
import { createHash } from 'node:crypto'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// 修复 crypto.hash 问题
if (!globalThis.crypto) {
  globalThis.crypto = {
    hash: createHash
  } as any
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  base: '/lowcode/',  // 开发和生产环境都使用 /lowcode/
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  define: {
    global: 'globalThis',
    // Vue 编译时特性标志
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
  },
  optimizeDeps: {
    exclude: ['crypto']
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: env.VITE_API_BASE_URL || 'http://localhost:8086',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          console.log('🔧 Vite Proxy Config:', {
            target: options.target,
            env_VITE_API_BASE_URL: env.VITE_API_BASE_URL,
            process_env_VITE_API_BASE_URL: process.env.VITE_API_BASE_URL
          })
        }
      },

    },
  },
  preview: {
    port: 4174,
    open: '/lowcode/',
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
}})
