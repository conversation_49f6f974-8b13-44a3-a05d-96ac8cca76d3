#!/usr/bin/env node

/**
 * 跨平台部署脚本 (Node.js版本)
 * 支持 Windows, macOS, Linux
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { Client } = require('ssh2');

// 配置
const config = {
  remote: {
    host: '**************',
    username: 'root',
    password: 'Pix@121cc.#',
    path: '/data/lowcode'
  },
  local: {
    buildDir: 'dist',
    archiveName: 'lowcode-frontend.tar.gz'
  }
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
  log(`[INFO] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`[SUCCESS] ${message}`, 'green');
}

function logError(message) {
  log(`[ERROR] ${message}`, 'red');
}

function logWarning(message) {
  log(`[WARNING] ${message}`, 'yellow');
}

// 检查依赖
function checkDependencies() {
  logInfo('检查依赖...');
  
  try {
    execSync('npm --version', { stdio: 'ignore' });
  } catch (error) {
    logError('npm 未安装，请先安装 Node.js');
    process.exit(1);
  }
  
  // 检查 ssh2 模块
  try {
    require('ssh2');
  } catch (error) {
    logWarning('ssh2 模块未安装，正在安装...');
    try {
      execSync('npm install ssh2', { stdio: 'inherit' });
      logSuccess('ssh2 模块安装完成');
    } catch (installError) {
      logError('ssh2 模块安装失败，请手动安装: npm install ssh2');
      process.exit(1);
    }
  }
  
  logSuccess('依赖检查完成');
}

// 打包项目
function buildProject() {
  return new Promise((resolve, reject) => {
    logInfo('开始打包前端项目...');
    
    // 检查 package.json
    if (!fs.existsSync('package.json')) {
      logError('未找到 package.json 文件，请确保在项目根目录执行脚本');
      reject(new Error('package.json not found'));
      return;
    }
    
    // 执行打包
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'inherit',
      shell: true
    });
    
    buildProcess.on('close', (code) => {
      if (code !== 0) {
        logError('项目打包失败');
        reject(new Error('Build failed'));
        return;
      }
      
      // 检查打包结果
      if (!fs.existsSync(config.local.buildDir)) {
        logError(`打包失败，未找到 ${config.local.buildDir} 目录`);
        reject(new Error('Build directory not found'));
        return;
      }
      
      logSuccess('项目打包完成');
      resolve();
    });
  });
}

// 创建压缩文件
function createArchive() {
  return new Promise((resolve, reject) => {
    logInfo('创建压缩文件...');
    
    const tar = require('tar');
    
    tar.create(
      {
        gzip: true,
        file: config.local.archiveName,
        cwd: config.local.buildDir
      },
      ['.']
    ).then(() => {
      logSuccess(`压缩文件创建完成: ${config.local.archiveName}`);
      resolve();
    }).catch((error) => {
      logError('压缩文件创建失败');
      reject(error);
    });
  });
}

// SSH 连接和操作
function deployToRemote() {
  return new Promise((resolve, reject) => {
    const conn = new Client();
    
    logInfo('连接远程服务器...');
    
    conn.on('ready', () => {
      logSuccess('远程服务器连接成功');
      
      // 删除现有目录
      const deleteCmd = `if [ -d '${config.remote.path}' ]; then rm -rf '${config.remote.path}'; echo '目录已删除'; else echo '目录不存在'; fi && mkdir -p '${config.remote.path}'`;
      
      conn.exec(deleteCmd, (err, stream) => {
        if (err) {
          logError('远程命令执行失败');
          reject(err);
          return;
        }
        
        stream.on('close', (code) => {
          if (code !== 0) {
            logError('远程目录处理失败');
            reject(new Error('Remote directory handling failed'));
            return;
          }
          
          logSuccess('远程目录处理完成');
          
          // 上传文件
          uploadFile(conn, resolve, reject);
        }).on('data', (data) => {
          console.log(data.toString());
        });
      });
    });
    
    conn.on('error', (err) => {
      logError('SSH 连接失败: ' + err.message);
      reject(err);
    });
    
    conn.connect({
      host: config.remote.host,
      username: config.remote.username,
      password: config.remote.password
    });
  });
}

// 上传文件
function uploadFile(conn, resolve, reject) {
  logInfo('上传压缩文件...');
  
  conn.sftp((err, sftp) => {
    if (err) {
      logError('SFTP 连接失败');
      reject(err);
      return;
    }
    
    const remoteTempPath = `/tmp/${config.local.archiveName}`;
    
    sftp.fastPut(config.local.archiveName, remoteTempPath, (err) => {
      if (err) {
        logError('文件上传失败');
        reject(err);
        return;
      }
      
      logSuccess('文件上传完成');
      
      // 解压文件
      const extractCmd = `cd '${config.remote.path}' && tar -xzf '${remoteTempPath}' && rm -f '${remoteTempPath}' && echo '文件解压完成' && echo '部署后的目录结构:' && ls -la | head -10`;
      
      conn.exec(extractCmd, (err, stream) => {
        if (err) {
          logError('文件解压失败');
          reject(err);
          return;
        }
        
        stream.on('close', (code) => {
          conn.end();
          
          if (code !== 0) {
            logError('文件解压失败');
            reject(new Error('File extraction failed'));
            return;
          }
          
          logSuccess('文件解压完成');
          resolve();
        }).on('data', (data) => {
          console.log(data.toString());
        });
      });
    });
  });
}

// 清理临时文件
function cleanup() {
  logInfo('清理临时文件...');
  
  if (fs.existsSync(config.local.archiveName)) {
    fs.unlinkSync(config.local.archiveName);
    logSuccess('临时文件清理完成');
  }
}

// 主函数
async function main() {
  try {
    console.log('================================');
    console.log('前端项目部署脚本 (Node.js版本)');
    console.log('================================');
    console.log(`远程服务器: ${config.remote.host}`);
    console.log(`部署路径: ${config.remote.path}`);
    console.log('================================');
    
    // 检查依赖
    checkDependencies();
    
    // 打包项目
    await buildProject();
    
    // 创建压缩文件
    await createArchive();
    
    // 部署到远程服务器
    await deployToRemote();
    
    // 清理临时文件
    cleanup();
    
    logSuccess('🎉 部署完成！');
    console.log('================================');
    console.log(`服务器: ${config.remote.host}`);
    console.log(`路径: ${config.remote.path}`);
    console.log('================================');
    
  } catch (error) {
    logError('部署失败: ' + error.message);
    cleanup();
    process.exit(1);
  }
}

// 执行主函数
if (require.main === module) {
  main();
}
