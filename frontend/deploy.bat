@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 部署脚本配置
set REMOTE_HOST=**************
set REMOTE_USER=root
set REMOTE_PASSWORD=Pix@121cc.#
set REMOTE_PATH=/data
set PROJECT_NAME=lowcode
set BUILD_DIR=dist
set ARCHIVE_NAME=lowcode-frontend.tar.gz

:: 颜色定义（Windows CMD不支持颜色，使用文本标识）
echo ================================
echo 前端项目部署脚本 (Windows版本)
echo ================================
echo 远程服务器: %REMOTE_HOST%
echo 部署路径: %REMOTE_PATH%/%PROJECT_NAME%
echo ================================

:: 检查依赖
echo [INFO] 检查依赖...

:: 检查 npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm 未安装，请先安装 Node.js
    pause
    exit /b 1
)

:: 检查 tar (Windows 10 1803+ 内置)
tar --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] tar 命令不可用，请使用 Windows 10 1803+ 或安装 Git Bash
    pause
    exit /b 1
)

:: 检查 scp (需要 OpenSSH 客户端)
scp >nul 2>&1
if errorlevel 1 (
    echo [ERROR] scp 命令不可用，请安装 OpenSSH 客户端
    echo 可以通过 Windows 功能或 Git Bash 安装
    pause
    exit /b 1
)

echo [SUCCESS] 依赖检查完成

:: 1. 打包压缩
echo [INFO] 开始打包前端项目...

:: 检查 package.json
if not exist "package.json" (
    echo [ERROR] 未找到 package.json 文件，请确保在项目根目录执行脚本
    pause
    exit /b 1
)

:: 安装依赖（如果需要）
if not exist "node_modules" (
    echo [INFO] 安装项目依赖...
    npm install
    if errorlevel 1 (
        echo [ERROR] 依赖安装失败
        pause
        exit /b 1
    )
)

:: 执行打包
echo [INFO] 执行打包命令...
npm run build
if errorlevel 1 (
    echo [ERROR] 项目打包失败
    pause
    exit /b 1
)

:: 检查打包结果
if not exist "%BUILD_DIR%" (
    echo [ERROR] 打包失败，未找到 %BUILD_DIR% 目录
    pause
    exit /b 1
)

echo [SUCCESS] 项目打包完成

:: 创建压缩文件
echo [INFO] 创建压缩文件...
cd %BUILD_DIR%
tar -czf "../%ARCHIVE_NAME%" *
cd ..

if not exist "%ARCHIVE_NAME%" (
    echo [ERROR] 压缩文件创建失败
    pause
    exit /b 1
)

echo [SUCCESS] 压缩文件创建完成: %ARCHIVE_NAME%

:: 2. 连接远程服务器并处理目录
echo [INFO] 连接远程服务器并处理目录...

:: 创建临时脚本文件用于远程执行
echo if [ -d '%REMOTE_PATH%/%PROJECT_NAME%' ]; then > temp_remote_script.sh
echo   echo '目录存在，正在删除...' >> temp_remote_script.sh
echo   rm -rf '%REMOTE_PATH%/%PROJECT_NAME%' >> temp_remote_script.sh
echo   echo '目录删除完成' >> temp_remote_script.sh
echo else >> temp_remote_script.sh
echo   echo '目录不存在，无需删除' >> temp_remote_script.sh
echo fi >> temp_remote_script.sh
echo mkdir -p '%REMOTE_PATH%' >> temp_remote_script.sh
echo echo '远程目录准备完成' >> temp_remote_script.sh

:: 上传并执行脚本
echo [INFO] 测试远程服务器连接...
echo y | plink -ssh -pw %REMOTE_PASSWORD% %REMOTE_USER%@%REMOTE_HOST% "echo '连接成功'"
if errorlevel 1 (
    echo [ERROR] 无法连接到远程服务器
    echo [INFO] 请确保已安装 PuTTY 或使用其他 SSH 客户端
    echo [INFO] 或者手动执行以下命令:
    echo ssh %REMOTE_USER%@%REMOTE_HOST%
    pause
    exit /b 1
)

echo [SUCCESS] 远程服务器连接成功

:: 上传脚本并执行
pscp -pw %REMOTE_PASSWORD% temp_remote_script.sh %REMOTE_USER%@%REMOTE_HOST%:/tmp/
echo y | plink -ssh -pw %REMOTE_PASSWORD% %REMOTE_USER%@%REMOTE_HOST% "chmod +x /tmp/temp_remote_script.sh && /tmp/temp_remote_script.sh && rm /tmp/temp_remote_script.sh"

echo [SUCCESS] 远程目录处理完成

:: 3. 上传并解压文件
echo [INFO] 上传压缩文件到远程服务器...

:: 上传文件
pscp -pw %REMOTE_PASSWORD% %ARCHIVE_NAME% %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PATH%/
if errorlevel 1 (
    echo [ERROR] 文件上传失败
    del temp_remote_script.sh >nul 2>&1
    del %ARCHIVE_NAME% >nul 2>&1
    pause
    exit /b 1
)

echo [SUCCESS] 文件上传完成

:: 在远程服务器解压
echo [INFO] 在远程服务器解压文件...
echo y | plink -ssh -pw %REMOTE_PASSWORD% %REMOTE_USER%@%REMOTE_HOST% "cd %REMOTE_PATH% && mkdir -p %PROJECT_NAME% && tar -xzf %ARCHIVE_NAME% -C %PROJECT_NAME% && rm -f %ARCHIVE_NAME% && echo '文件解压完成' && echo '部署后的目录结构:' && ls -la %REMOTE_PATH%/%PROJECT_NAME% | head -10"

echo [SUCCESS] 文件解压完成

:: 清理临时文件
echo [INFO] 清理临时文件...
del temp_remote_script.sh >nul 2>&1
del %ARCHIVE_NAME% >nul 2>&1
echo [SUCCESS] 临时文件清理完成

echo [SUCCESS] 部署完成！
echo ================================
echo 前端项目已成功部署到:
echo 服务器: %REMOTE_HOST%
echo 路径: %REMOTE_PATH%/%PROJECT_NAME%
echo ================================

pause
