# 前端项目部署脚本

本项目提供了多种部署脚本，用于将前端项目自动部署到远程服务器。

## 部署配置

- **远程服务器**: 47.106.248.211
- **用户名**: root
- **密码**: Pix@121cc.#
- **部署路径**: /data/lowcode

## 可用脚本

### 1. deploy.sh (推荐 - Linux/macOS)

功能最完整的Shell脚本，包含详细的错误处理和日志输出。

**依赖要求:**
```bash
# Ubuntu/Debian
sudo apt-get install sshpass

# macOS
brew install sshpass

# CentOS/RHEL
sudo yum install sshpass
```

**使用方法:**
```bash
chmod +x deploy.sh
./deploy.sh
```

### 2. deploy-simple.sh (简化版 - Linux/macOS)

使用rsync的简化版本，更高效的文件同步。

**依赖要求:**
```bash
# 需要 sshpass 和 rsync
sudo apt-get install sshpass rsync  # Ubuntu/Debian
brew install sshpass rsync          # macOS
```

**使用方法:**
```bash
chmod +x deploy-simple.sh
./deploy-simple.sh
```

### 3. deploy.bat (Windows)

Windows批处理脚本版本。

**依赖要求:**
- Windows 10 1803+ (内置tar命令)
- PuTTY工具集 (plink, pscp)
- 或者安装Git Bash

**使用方法:**
```cmd
deploy.bat
```

### 4. deploy.js (跨平台 - Node.js)

使用Node.js编写的跨平台脚本，支持所有操作系统。

**依赖要求:**
```bash
# 自动安装依赖，或手动安装
npm install ssh2 tar
```

**使用方法:**
```bash
node deploy.js
# 或者
chmod +x deploy.js
./deploy.js
```

## 部署流程

所有脚本都执行以下步骤：

1. **环境检查**: 验证必要的工具和依赖
2. **项目打包**: 执行 `npm run build` 构建项目
3. **文件压缩**: 将dist目录打包为tar.gz文件
4. **远程连接**: 连接到远程服务器
5. **目录清理**: 删除现有的 `/data/lowcode` 目录
6. **文件上传**: 上传压缩文件到服务器
7. **文件解压**: 在服务器上解压到 `/data/lowcode`
8. **清理工作**: 删除临时文件

## 故障排除

### 1. SSH连接失败

**问题**: 无法连接到远程服务器
**解决方案**:
- 检查网络连接
- 确认服务器IP和端口
- 验证用户名和密码
- 检查防火墙设置

### 2. sshpass未安装

**问题**: `sshpass: command not found`
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install sshpass

# macOS
brew install sshpass

# CentOS/RHEL
sudo yum install epel-release && sudo yum install sshpass
```

### 3. 权限问题

**问题**: `Permission denied`
**解决方案**:
```bash
# 给脚本添加执行权限
chmod +x deploy.sh
chmod +x deploy-simple.sh
chmod +x deploy.js
```

### 4. Node.js依赖问题

**问题**: 模块未找到
**解决方案**:
```bash
# 安装必要的Node.js模块
npm install ssh2 tar
```

### 5. Windows特定问题

**问题**: Windows下命令不可用
**解决方案**:
- 安装Git Bash或WSL
- 安装PuTTY工具集
- 使用Windows 10 1803+版本

## 安全注意事项

⚠️ **重要**: 这些脚本包含明文密码，仅用于开发环境。

**生产环境建议**:
1. 使用SSH密钥认证替代密码
2. 将敏感信息存储在环境变量中
3. 使用配置文件而非硬编码
4. 实施适当的访问控制

## 自定义配置

如需修改部署配置，请编辑脚本中的以下变量：

```bash
# Shell脚本
REMOTE_HOST="your-server-ip"
REMOTE_USER="your-username"
REMOTE_PASSWORD="your-password"
REMOTE_PATH="/your/deploy/path"

# Node.js脚本
const config = {
  remote: {
    host: 'your-server-ip',
    username: 'your-username',
    password: 'your-password',
    path: '/your/deploy/path'
  }
};
```

## 支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 服务器凭据是否正确
3. 必要的工具是否已安装
4. 脚本是否有执行权限

---

**推荐使用顺序**:
1. Linux/macOS: `deploy.sh` (功能最完整)
2. Linux/macOS: `deploy-simple.sh` (简单快速)
3. 跨平台: `deploy.js` (Node.js环境)
4. Windows: `deploy.bat` (Windows专用)
