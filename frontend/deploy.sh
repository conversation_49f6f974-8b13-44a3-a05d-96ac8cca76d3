#!/bin/bash

# 部署脚本配置
REMOTE_HOST="**************"
REMOTE_USER="root"
REMOTE_PASSWORD="Pix@121cc.#"
REMOTE_PATH="/data"
PROJECT_NAME="lowcode"
BUILD_DIR="dist"
ARCHIVE_NAME="lowcode-frontend.tar.gz"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 sshpass
    if ! command -v sshpass &> /dev/null; then
        log_error "sshpass 未安装，请先安装："
        echo "  Ubuntu/Debian: sudo apt-get install sshpass"
        echo "  CentOS/RHEL: sudo yum install sshpass"
        echo "  macOS: brew install sshpass"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 Node.js"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    if [ -f "$ARCHIVE_NAME" ]; then
        rm -f "$ARCHIVE_NAME"
        log_success "已删除临时压缩文件"
    fi
}

# 错误处理
handle_error() {
    log_error "部署过程中发生错误，正在清理..."
    cleanup
    exit 1
}

# 设置错误处理
trap handle_error ERR

# 1. 打包压缩
build_project() {
    log_info "开始打包前端项目..."
    
    # 检查是否存在 package.json
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json 文件，请确保在项目根目录执行脚本"
        exit 1
    fi
    
    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        log_info "安装项目依赖..."
        npm install
    fi
    
    # 执行打包
    log_info "执行打包命令..."
    npm run build
    
    # 检查打包结果
    if [ ! -d "$BUILD_DIR" ]; then
        log_error "打包失败，未找到 $BUILD_DIR 目录"
        exit 1
    fi
    
    log_success "项目打包完成"
    
    # 创建压缩文件
    log_info "创建压缩文件..."
    tar -czf "$ARCHIVE_NAME" -C "$BUILD_DIR" .
    
    if [ ! -f "$ARCHIVE_NAME" ]; then
        log_error "压缩文件创建失败"
        exit 1
    fi
    
    log_success "压缩文件创建完成: $ARCHIVE_NAME"
}

# 2. 连接远程服务器并处理目录
handle_remote_directory() {
    log_info "连接远程服务器并处理目录..."
    
    # 测试连接
    log_info "测试远程服务器连接..."
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo '连接成功'" || {
        log_error "无法连接到远程服务器"
        exit 1
    }
    
    log_success "远程服务器连接成功"
    
    # 检查并删除现有目录
    log_info "检查远程目录 $REMOTE_PATH/$PROJECT_NAME..."
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        if [ -d '$REMOTE_PATH/$PROJECT_NAME' ]; then
            echo '检测到现有目录，正在删除以防止文件冲突...'
            rm -rf '$REMOTE_PATH/$PROJECT_NAME'
            echo '现有目录删除完成'
        else
            echo '目录不存在，无需删除'
        fi

        # 确保 /data 目录存在
        mkdir -p '$REMOTE_PATH'
        echo '远程目录准备完成'
    "
    
    log_success "远程目录处理完成（已清理旧文件，防止冲突）"
}

# 3. 上传并解压文件
upload_and_extract() {
    log_info "上传压缩文件到远程服务器..."
    
    # 上传文件
    sshpass -p "$REMOTE_PASSWORD" scp -o StrictHostKeyChecking=no "$ARCHIVE_NAME" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/" || {
        log_error "文件上传失败"
        exit 1
    }
    
    log_success "文件上传完成"
    
    # 在远程服务器解压
    log_info "在远程服务器解压文件..."
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        cd '$REMOTE_PATH'
        mkdir -p '$PROJECT_NAME'
        tar -xzf '$ARCHIVE_NAME' -C '$PROJECT_NAME'
        rm -f '$ARCHIVE_NAME'
        echo '文件解压完成'
        
        # 显示目录结构
        echo '部署后的目录结构:'
        ls -la '$REMOTE_PATH/$PROJECT_NAME' | head -10
    "
    
    log_success "文件解压完成"
}

# 主函数
main() {
    log_info "开始部署前端项目..."
    echo "=================================="
    echo "远程服务器: $REMOTE_HOST"
    echo "部署路径: $REMOTE_PATH/$PROJECT_NAME"
    echo "注意: 部署过程会先删除现有目录以防止文件冲突"
    echo "=================================="
    
    # 检查依赖
    check_dependencies
    
    # 1. 打包压缩
    build_project
    
    # 2. 处理远程目录
    handle_remote_directory
    
    # 3. 上传并解压
    upload_and_extract
    
    # 清理临时文件
    cleanup
    
    log_success "部署完成！"
    echo "=================================="
    echo "前端项目已成功部署到:"
    echo "服务器: $REMOTE_HOST"
    echo "路径: $REMOTE_PATH/$PROJECT_NAME"
    echo "=================================="
}

# 执行主函数
main "$@"
