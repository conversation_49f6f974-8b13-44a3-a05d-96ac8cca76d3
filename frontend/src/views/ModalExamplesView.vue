<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">模态框组件示例</h1>
    
    <!-- 预设模态框示例 -->
    <div class="card bg-base-100 shadow-xl mb-8">
      <div class="card-body">
        <h2 class="card-title">预设模态框</h2>
        <p class="text-base-content/70 mb-4">使用预设配置快速创建常用模态框</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <button class="btn btn-warning" @click="showDeleteExample">
            删除确认
          </button>
          
          <button class="btn btn-primary" @click="showSaveExample">
            保存确认
          </button>
          
          <button class="btn btn-error" @click="showErrorExample">
            错误提示
          </button>
          
          <button class="btn btn-success" @click="showSuccessExample">
            成功提示
          </button>
          
          <button class="btn btn-info" @click="showInfoExample">
            信息提示
          </button>
        </div>
      </div>
    </div>

    <!-- 快速方法示例 -->
    <div class="card bg-base-100 shadow-xl mb-8">
      <div class="card-body">
        <h2 class="card-title">快速方法</h2>
        <p class="text-base-content/70 mb-4">使用便捷方法创建模态框</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button class="btn btn-outline btn-warning" @click="showConfirmExample">
            确认对话框
          </button>
          
          <button class="btn btn-outline btn-info" @click="showAlertExample">
            提示对话框
          </button>
        </div>
      </div>
    </div>

    <!-- 自定义模态框示例 -->
    <div class="card bg-base-100 shadow-xl mb-8">
      <div class="card-body">
        <h2 class="card-title">自定义模态框</h2>
        <p class="text-base-content/70 mb-4">创建完全自定义的模态框</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button class="btn btn-outline btn-primary" @click="showCustomExample">
            自定义配置
          </button>
          
          <button class="btn btn-outline btn-secondary" @click="showAsyncExample">
            异步操作
          </button>
        </div>
      </div>
    </div>

    <!-- 模态框类型展示 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">模态框类型</h2>
        <p class="text-base-content/70 mb-4">展示不同类型的模态框样式</p>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button class="btn btn-warning btn-sm" @click="showTypeExample('warning')">
            Warning
          </button>
          
          <button class="btn btn-error btn-sm" @click="showTypeExample('danger')">
            Danger
          </button>
          
          <button class="btn btn-info btn-sm" @click="showTypeExample('info')">
            Info
          </button>
          
          <button class="btn btn-success btn-sm" @click="showTypeExample('success')">
            Success
          </button>
        </div>
      </div>
    </div>

    <!-- 通用模态框组件 -->
    <BaseModal
      v-model:show="modal.show.value"
      :config="modal.config.value"
      @confirm="modal.handleConfirm"
      @cancel="modal.handleCancel"
      @button-click="modal.handleButtonClick"
    >
      <!-- 自定义内容插槽示例 -->
      <div v-if="showCustomContent" class="mt-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">输入内容</span>
          </label>
          <input 
            v-model="customInput"
            type="text" 
            class="input input-bordered" 
            placeholder="请输入内容..."
          />
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useModal } from '@/composables/useModal'
import { createModalConfig, createButton } from '@/utils/modal'
import BaseModal from '@/components/common/BaseModal.vue'
import type { ModalConfig } from '@/types/modal'

// 状态
const modal = useModal()
const showCustomContent = ref(false)
const customInput = ref('')

// 预设模态框示例
const showDeleteExample = () => {
  modal.showDeleteConfirm('重要配置文件', async () => {
    // 模拟删除操作
    await new Promise(resolve => setTimeout(resolve, 1500))
    console.log('删除完成')
  })
}

const showSaveExample = () => {
  modal.showSaveConfirm(async () => {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('保存完成')
  })
}

const showErrorExample = () => {
  modal.showError('网络连接失败，请检查网络设置后重试')
}

const showSuccessExample = () => {
  modal.showSuccess('数据保存成功，所有更改已生效')
}

const showInfoExample = () => {
  modal.showInfo('系统将在 5 分钟后进行维护，请及时保存工作')
}

// 快速方法示例
const showConfirmExample = () => {
  modal.confirm(
    '确认提交',
    '提交后将无法修改，确定要继续吗？',
    {
      confirmText: '提交',
      cancelText: '取消',
      onConfirm: async () => {
        await new Promise(resolve => setTimeout(resolve, 1000))
        console.log('提交完成')
      },
      onCancel: () => {
        console.log('用户取消了操作')
      }
    }
  )
}

const showAlertExample = () => {
  modal.alert('info', '系统通知', '您有 3 条新消息待查看')
}

// 自定义模态框示例
const showCustomExample = () => {
  showCustomContent.value = true
  
  const config = createModalConfig('info', '自定义模态框', {
    subtitle: '这是一个完全自定义的模态框',
    description: '您可以在下方输入任何内容',
    size: 'lg',
    buttons: [
      createButton('取消', 'ghost', () => {
        showCustomContent.value = false
        customInput.value = ''
      }),
      createButton('确认', 'primary', async () => {
        if (!customInput.value.trim()) {
          modal.showError('请输入内容')
          throw new Error('输入为空')
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        console.log('用户输入:', customInput.value)
        
        showCustomContent.value = false
        customInput.value = ''
        
        // 显示成功提示
        setTimeout(() => {
          modal.showSuccess(`已保存内容: ${customInput.value}`)
        }, 100)
      })
    ]
  })
  
  modal.showModal(config)
}

const showAsyncExample = () => {
  modal.confirm(
    '异步操作示例',
    '这个操作需要一些时间完成',
    {
      confirmText: '开始处理',
      onConfirm: async () => {
        // 模拟长时间异步操作
        for (let i = 0; i < 3; i++) {
          await new Promise(resolve => setTimeout(resolve, 1000))
          console.log(`处理步骤 ${i + 1}/3`)
        }
        
        // 随机成功或失败
        if (Math.random() > 0.3) {
          console.log('异步操作成功完成')
        } else {
          throw new Error('操作失败，请重试')
        }
      }
    }
  )
}

// 类型示例
const showTypeExample = (type: ModalConfig['type']) => {
  const typeConfig = {
    warning: {
      title: '警告提示',
      description: '这是一个警告类型的模态框，用于需要用户注意的操作'
    },
    danger: {
      title: '危险操作',
      description: '这是一个危险类型的模态框，用于删除或其他不可逆操作'
    },
    info: {
      title: '信息提示',
      description: '这是一个信息类型的模态框，用于向用户展示重要信息'
    },
    success: {
      title: '操作成功',
      description: '这是一个成功类型的模态框，用于确认操作已成功完成'
    }
  }
  
  const config = typeConfig[type]
  modal.alert(type, config.title, config.description)
}
</script>
