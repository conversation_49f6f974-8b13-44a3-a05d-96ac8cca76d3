/**
 * 模态框工具函数和预设配置
 */

import type { ModalConfig, ModalButton, PresetModalConfigs } from '@/types/modal'

// 创建标准按钮配置
export const createButton = (
  text: string,
  variant: ModalButton['variant'] = 'primary',
  onClick?: () => void | Promise<void>
): ModalButton => ({
  text,
  variant,
  onClick
})

// 创建取消按钮
export const createCancelButton = (onClick?: () => void): ModalButton => 
  createButton('取消', 'ghost', onClick)

// 创建确认按钮
export const createConfirmButton = (
  text: string = '确认',
  variant: ModalButton['variant'] = 'primary',
  onClick?: () => void | Promise<void>
): ModalButton => createButton(text, variant, onClick)

// 创建删除按钮
export const createDeleteButton = (onClick?: () => void | Promise<void>): ModalButton => 
  createButton('确认删除', 'error', onClick)

// 预设模态框配置
export const modalPresets: PresetModalConfigs = {
  // 删除确认模态框
  deleteConfirm: (itemName: string): ModalConfig => ({
    type: 'warning',
    title: '确认删除配置',
    subtitle: `配置名称：${itemName}`,
    description: '此操作将永久删除配置，无法恢复！',
    buttons: [
      createCancelButton(),
      createDeleteButton()
    ],
    closable: true,
    size: 'md'
  }),

  // 保存确认模态框
  saveConfirm: (): ModalConfig => ({
    type: 'warning',
    title: '确认保存',
    description: '是否保存当前配置的更改？',
    buttons: [
      createCancelButton(),
      createConfirmButton('保存', 'primary')
    ],
    closable: true,
    size: 'md'
  }),

  // 错误提示模态框
  errorAlert: (message: string): ModalConfig => ({
    type: 'danger',
    title: '操作失败',
    description: message,
    buttons: [
      createConfirmButton('知道了', 'error')
    ],
    closable: true,
    size: 'md'
  }),

  // 成功提示模态框
  successAlert: (message: string): ModalConfig => ({
    type: 'success',
    title: '操作成功',
    description: message,
    buttons: [
      createConfirmButton('知道了', 'success')
    ],
    closable: true,
    size: 'md'
  }),

  // 信息提示模态框
  infoAlert: (message: string): ModalConfig => ({
    type: 'info',
    title: '提示信息',
    description: message,
    buttons: [
      createConfirmButton('知道了', 'info')
    ],
    closable: true,
    size: 'md'
  })
}

// 创建自定义模态框配置的辅助函数
export const createModalConfig = (
  type: ModalConfig['type'],
  title: string,
  options: Partial<Omit<ModalConfig, 'type' | 'title'>> = {}
): ModalConfig => ({
  type,
  title,
  buttons: [createConfirmButton()],
  closable: true,
  size: 'md',
  ...options
})

// 快速创建确认对话框
export const createConfirmDialog = (
  title: string,
  description?: string,
  options: {
    type?: ModalConfig['type']
    confirmText?: string
    cancelText?: string
    confirmVariant?: ModalButton['variant']
  } = {}
): ModalConfig => {
  const {
    type = 'warning',
    confirmText = '确认',
    cancelText = '取消',
    confirmVariant = 'primary'
  } = options

  return {
    type,
    title,
    description,
    buttons: [
      createButton(cancelText, 'ghost'),
      createButton(confirmText, confirmVariant)
    ],
    closable: true,
    size: 'md'
  }
}

// 快速创建单按钮提示框
export const createAlertDialog = (
  type: ModalConfig['type'],
  title: string,
  description?: string,
  buttonText: string = '知道了'
): ModalConfig => {
  const variantMap = {
    warning: 'warning' as const,
    danger: 'error' as const,
    info: 'info' as const,
    success: 'success' as const
  }

  return {
    type,
    title,
    description,
    buttons: [
      createButton(buttonText, variantMap[type])
    ],
    closable: true,
    size: 'md'
  }
}
