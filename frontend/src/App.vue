<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { RouterView, useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 初始化认证状态
onMounted(() => {
  authStore.initAuth()
})

const handleLogout = async () => {
  try {
    await authStore.logout()
    // 手动跳转到登录页
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
    // 即使API调用失败，也要清除本地状态并跳转
    authStore.clearAuth()
    router.push('/login')
  }
}

const isLoginPage = computed(() => route.path === '/login')

// 测试页面列表 - 这些页面允许滚动
const testPages: string[] = []

// 判断是否为测试页面
const isTestPage = computed(() => testPages.includes(route.path))

// 业务页面列表 - 这些页面需要固定高度
const businessPages = [
  '/',
  '/orders',
  '/lowcode',
  '/lowcode/runtime'
]

// 判断是否为业务页面
const isBusinessPage = computed(() => businessPages.includes(route.path))
</script>

<template>
  <!-- 登录页面不显示导航栏 -->
  <div v-if="!isLoginPage" class="h-screen bg-base-200 flex flex-col">
    <!-- 导航栏 -->
    <div class="navbar bg-base-100 shadow-lg flex-shrink-0">
      <div class="navbar-start">
        <div class="dropdown">
          <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
            </svg>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li><RouterLink to="/">首页</RouterLink></li>
            <li><RouterLink to="/orders">订单管理</RouterLink></li>

            <li><RouterLink to="/config">低代码配置</RouterLink></li>
            <li><RouterLink to="/runtime">低代码演示</RouterLink></li>
          </ul>
        </div>
        <RouterLink to="/" class="btn btn-ghost text-xl">CCAPI 管理系统</RouterLink>
      </div>

      <div class="navbar-center hidden lg:flex">
        <ul class="menu menu-horizontal px-1">
          <li><RouterLink to="/" class="btn btn-ghost">首页</RouterLink></li>
          <li><RouterLink to="/orders" class="btn btn-ghost">订单管理</RouterLink></li>

          <li><RouterLink to="/config" class="btn btn-ghost">低代码配置</RouterLink></li>
          <li><RouterLink to="/runtime" class="btn btn-ghost">低代码演示</RouterLink></li>
        </ul>
      </div>

      <div class="navbar-end">
        <!-- 用户信息显示 -->
        <div class="flex items-center gap-2 mr-4">
          <span class="text-sm text-base-content/70">欢迎，</span>
          <span class="text-sm font-medium">{{ authStore.user?.username || 'admin' }}</span>
        </div>

        <!-- 用户头像下拉菜单 -->
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
            <div class="w-10 h-10 rounded-full bg-primary text-primary-content" style="display: flex; align-items: center; justify-content: center; position: relative;">
              <svg style="width: 20px; height: 20px; fill: currentColor; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
              </svg>
            </div>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li class="menu-title">
              <span class="text-base-content/70">{{ authStore.user?.username || 'admin' }}</span>
            </li>
            <li><a class="flex items-center gap-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              个人设置
            </a></li>
            <li><a @click="handleLogout" class="flex items-center gap-2 text-error">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              退出登录
            </a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main
      class="flex-1"
      :class="{
        'overflow-hidden': isBusinessPage,
        'overflow-auto': isTestPage
      }"
    >
      <!-- 测试页面标识 -->
      <div v-if="isTestPage" class="bg-warning/10 border-l-4 border-warning px-4 py-2 text-sm">
        <div class="flex items-center gap-2">
          <svg class="w-4 h-4 text-warning" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
          <span class="text-warning font-medium">测试页面</span>
          <span class="text-base-content/60">- 此页面允许滚动查看完整内容</span>
        </div>
      </div>

      <RouterView />
    </main>
  </div>

  <!-- 登录页面 -->
  <div v-else>
    <RouterView />
  </div>
</template>

<style scoped>
.router-link-active {
  @apply text-primary;
}
</style>
