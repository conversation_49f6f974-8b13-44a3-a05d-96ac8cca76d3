<template>
  <div class="space-y-4">
    <!-- 参数列表 -->
    <div v-if="localParams.length === 0" class="text-center py-8 text-base-content/60">
      <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
      </svg>
      <p>暂无参数配置</p>
      <p class="text-sm">点击下方按钮添加动态参数</p>
    </div>

    <div v-else class="space-y-3">
      <div 
        v-for="(param, index) in localParams" 
        :key="index"
        class="card bg-base-100 border p-4"
        :class="{ 'border-primary': param.enabled, 'border-base-300 opacity-60': !param.enabled }"
      >
        <div class="flex items-start gap-4">
          <!-- 启用开关 -->
          <input 
            v-model="param.enabled"
            type="checkbox" 
            class="toggle toggle-primary toggle-sm mt-1"
          />

          <!-- 参数配置 -->
          <div class="flex-1 space-y-3">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div class="form-control">
                <label class="label label-text-sm">
                  <span class="label-text">参数名 *</span>
                </label>
                <input 
                  v-model="param.key"
                  type="text" 
                  class="input input-bordered input-sm"
                  placeholder="参数名"
                  required
                />
              </div>

              <div class="form-control">
                <label class="label label-text-sm">
                  <span class="label-text">类型</span>
                </label>
                <select 
                  v-model="param.type"
                  class="select select-bordered select-sm"
                >
                  <option value="static">固定值</option>
                  <option value="function">函数调用</option>
                  <option value="expression">表达式</option>
                  <option value="template">模板字符串</option>
                  <option value="array">数组</option>
                </select>
              </div>
            </div>

            <!-- 值配置 -->
            <div v-if="param.type !== 'array'" class="form-control">
              <label class="label label-text-sm">
                <span class="label-text">值</span>
                <span class="label-text-alt">{{ getTypeHint(param.type) }}</span>
              </label>
              <div class="flex gap-2">
                <input
                  v-model="param.value"
                  type="text"
                  class="input input-bordered input-sm flex-1"
                  :placeholder="getTypePlaceholder(param.type)"
                  @input="onValueChange(param, index)"
                />
                <button
                  v-if="param.type === 'function'"
                  class="btn btn-outline btn-sm"
                  @click="showFunctionHelper(index)"
                >
                  函数
                </button>
              </div>

              <!-- 函数参数输入区域 -->
              <div v-if="param.type === 'function' && getFunctionInfo(param.value) && getFunctionInfo(param.value)?.parameters && (getFunctionInfo(param.value)?.parameters?.length || 0) > 0" class="mt-3 p-3 bg-base-100 border rounded">
                <div class="text-sm font-medium mb-2">函数参数配置</div>
                <div
                  v-for="(funcParam, paramIndex) in getFunctionInfo(param.value)?.parameters || []"
                  :key="paramIndex"
                  class="grid grid-cols-2 gap-2 mb-2"
                >
                  <div class="form-control">
                    <label class="label label-text-xs">
                      <span class="label-text">{{ funcParam.name }}</span>
                      <span v-if="funcParam.required" class="text-error">*</span>
                    </label>
                    <input
                      v-model="getFunctionParams(param, index)[funcParam.name]"
                      :type="getInputType(funcParam.type)"
                      class="input input-bordered input-xs"
                      :placeholder="funcParam.description"
                      :required="funcParam.required"
                      @input="updateFunctionCall(param, index)"
                    />
                  </div>
                  <div class="form-control">
                    <label class="label label-text-xs">
                      <span class="label-text">说明</span>
                    </label>
                    <div class="text-xs text-base-content/60 p-2 bg-base-200 rounded">
                      {{ funcParam.description }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 数组元素配置 -->
            <div v-if="param.type === 'array'" class="space-y-2">
              <label class="label label-text-sm">
                <span class="label-text">数组元素</span>
              </label>
              
              <div v-if="!param.arrayElements || param.arrayElements.length === 0" class="text-center py-4 border-2 border-dashed border-base-300 rounded">
                <p class="text-sm text-base-content/60">暂无数组元素</p>
              </div>

              <div v-else class="space-y-2">
                <div
                  v-for="(element, elementIndex) in param.arrayElements"
                  :key="elementIndex"
                  class="space-y-2"
                >
                  <div class="flex gap-2 items-center p-2 bg-base-200 rounded">
                    <select
                      v-model="element.type"
                      class="select select-bordered select-xs w-24"
                    >
                      <option value="static">固定值</option>
                      <option value="function">函数</option>
                      <option value="expression">表达式</option>
                      <option value="template">模板</option>
                    </select>

                    <div class="flex gap-1 flex-1">
                      <input
                        v-model="element.value"
                        type="text"
                        class="input input-bordered input-xs flex-1"
                        :placeholder="getTypePlaceholder(element.type)"
                      />
                      <button
                        v-if="element.type === 'function'"
                        type="button"
                        class="btn btn-xs btn-outline"
                        @click="openFunctionModalForArrayElement(index, elementIndex)"
                      >
                        函数
                      </button>
                    </div>

                    <button
                      class="btn btn-ghost btn-xs text-error"
                      @click="removeArrayElement(index, elementIndex)"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>

                  <!-- 数组元素函数参数配置 -->
                  <div v-if="element.type === 'function' && getFunctionInfo(element.value) && getFunctionInfo(element.value)?.parameters && (getFunctionInfo(element.value)?.parameters?.length || 0) > 0" class="ml-4 p-3 bg-base-100 border rounded">
                    <div class="text-sm font-medium mb-2">函数参数配置</div>
                    <div
                      v-for="(funcParam, paramIndex) in getFunctionInfo(element.value)?.parameters || []"
                      :key="paramIndex"
                      class="grid grid-cols-2 gap-2 mb-2"
                    >
                      <div class="form-control">
                        <label class="label label-text-xs">
                          <span class="label-text">{{ funcParam.name }}</span>
                          <span v-if="funcParam.required" class="text-error">*</span>
                        </label>
                        <input
                          v-model="getArrayElementFunctionParams(index, elementIndex)[funcParam.name]"
                          :type="getInputType(funcParam.type)"
                          class="input input-bordered input-xs"
                          :placeholder="funcParam.description"
                          :required="funcParam.required"
                          @input="updateArrayElementFunctionCall(index, elementIndex)"
                        />
                      </div>
                      <div class="form-control">
                        <label class="label label-text-xs">
                          <span class="label-text">说明</span>
                        </label>
                        <div class="text-xs text-base-content/60 p-2 bg-base-200 rounded">
                          {{ funcParam.description }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <button 
                class="btn btn-outline btn-xs"
                @click="addArrayElement(index)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                添加元素
              </button>
            </div>

            <!-- 描述与预览 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 items-end">
              <!-- 描述 -->
              <div class="form-control">
                <label class="label label-text-sm">
                  <span class="label-text">描述</span>
                </label>
                <input
                  v-model="param.description"
                  type="text"
                  class="input input-bordered input-sm h-10"
                  placeholder="参数描述（可选）"
                />
              </div>

              <!-- 预览值 -->
              <div class="form-control">
                <label class="label label-text-sm">
                  <span class="label-text">预览</span>
                  <button
                    v-if="param.enabled && param.key && param.value"
                    class="btn btn-xs btn-ghost"
                    @click="refreshPreview(index)"
                    title="刷新预览"
                    :disabled="previewLoading[index]"
                  >
                    <span v-if="previewLoading[index]" class="loading loading-spinner loading-xs"></span>
                    <svg v-else class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                  </button>
                </label>
                <div class="bg-base-200 p-2 rounded text-sm font-mono h-10 flex items-center">
                  <span v-if="!param.enabled || !param.key || !param.value" class="text-base-content/50">
                    请配置完整参数信息
                  </span>
                  <span v-else-if="previewValues[index] === undefined" class="text-base-content/50">
                    点击刷新按钮预览值
                  </span>
                  <span v-else-if="previewValues[index] && previewValues[index].startsWith('错误:')" class="text-error">
                    {{ previewValues[index] }}
                  </span>
                  <span v-else class="text-success">
                    {{ previewValues[index] }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col gap-1">
            <button 
              class="btn btn-ghost btn-xs text-error"
              @click="removeParam(index)"
              title="删除参数"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加参数按钮 -->
    <div class="flex gap-2">
      <button
        class="btn btn-outline btn-sm"
        @click="addParam"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
        </svg>
        添加参数
      </button>

      <button
        v-if="localParams.length > 0"
        class="btn btn-outline btn-sm"
        @click="refreshAllPreviews"
        :disabled="Object.values(previewLoading).some(loading => loading)"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        刷新所有预览
      </button>


    </div>

    <!-- 函数帮助模态框 -->
    <FunctionHelperModal 
      v-model:show="showFunctionModal"
      @select="selectFunction"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue'
import type { DynamicParamConfig, ArrayElementConfig } from '@/types/lowcode'
import { dynamicParamResolver } from '@/utils/dynamic-params'
import FunctionHelperModal from './FunctionHelperModal.vue'

// Props
const props = defineProps<{
  modelValue: DynamicParamConfig[]
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: DynamicParamConfig[]]
}>()

// 本地状态
const localParams = ref<DynamicParamConfig[]>([...props.modelValue])
const showFunctionModal = ref(false)
const currentEditingIndex = ref(-1)
const currentEditingArrayElement = ref<{ paramIndex: number; elementIndex: number } | null>(null)
const previewValues = ref<Record<number, string>>({})
const previewLoading = ref<Record<number, boolean>>({})
const functionParams = ref<Record<number, Record<string, any>>>({}) // 存储函数参数值
const arrayElementFunctionParams = ref<Record<string, Record<string, any>>>({}) // 存储数组元素函数参数值

// 调试日志
console.log('🔍 [DynamicParamsConfig] props.modelValue:', props.modelValue)
console.log('🔍 [DynamicParamsConfig] localParams.value:', localParams.value)

// 标记是否正在内部更新，避免递归
let isInternalUpdate = false

// 监听变化
watch(localParams, (newValue) => {
  if (!isInternalUpdate) {
    emit('update:modelValue', newValue)
  }
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) !== JSON.stringify(localParams.value)) {
    isInternalUpdate = true
    localParams.value = [...newValue]
    // 清空预览值，需要重新刷新
    previewValues.value = {}

    // 重新初始化函数参数
    functionParams.value = {}
    arrayElementFunctionParams.value = {}

    // 使用 nextTick 确保在下一个 tick 中初始化函数参数
    nextTick(() => {
      // 为每个函数类型的参数初始化参数值
      newValue.forEach((param, index) => {
        if (param.type === 'function') {
          // 触发函数参数初始化
          getFunctionParams(param, index)
        }

        // 处理数组元素中的函数
        if (param.type === 'array' && param.arrayElements) {
          param.arrayElements.forEach((element, elementIndex) => {
            if (element.type === 'function') {
              // 触发数组元素函数参数初始化
              getArrayElementFunctionParams(index, elementIndex)
            }
          })
        }
      })

      // 重置标记
      isInternalUpdate = false
    })

    console.log('🔍 [DynamicParamsConfig] Reinitialized function params:', functionParams.value)
    console.log('🔍 [DynamicParamsConfig] Reinitialized array element function params:', arrayElementFunctionParams.value)
  }
}, { deep: true })

// 监听参数值变化，自动刷新预览（防抖）
const previewTimeouts: Record<number, ReturnType<typeof setTimeout>> = {}

watch(localParams, (newValue, oldValue) => {
  // 避免在内部更新时触发预览刷新
  if (isInternalUpdate) {
    return
  }

  newValue.forEach((param, index) => {
    const oldParam = oldValue?.[index]
    if (oldParam && (
      param.value !== oldParam.value ||
      param.type !== oldParam.type ||
      param.enabled !== oldParam.enabled
    )) {
      // 清除之前的定时器
      if (previewTimeouts[index]) {
        clearTimeout(previewTimeouts[index])
      }

      // 设置新的定时器，500ms后刷新预览
      previewTimeouts[index] = setTimeout(() => {
        if (param.enabled && param.key && param.value) {
          refreshPreview(index)
        } else {
          previewValues.value[index] = ''
        }
      }, 500)
    }
  })
}, { deep: true })

// 方法
const addParam = () => {
  localParams.value.push({
    key: '',
    type: 'static',
    value: '',
    description: '',
    enabled: true
  })
}

const removeParam = (index: number) => {
  if (confirm('确定要删除这个参数吗？')) {
    localParams.value.splice(index, 1)
  }
}

const addArrayElement = (paramIndex: number) => {
  const param = localParams.value[paramIndex]
  if (!param.arrayElements) {
    param.arrayElements = []
  }
  param.arrayElements.push({
    type: 'static',
    value: '',
    description: ''
  })
}

const removeArrayElement = (paramIndex: number, elementIndex: number) => {
  const param = localParams.value[paramIndex]
  if (param.arrayElements) {
    param.arrayElements.splice(elementIndex, 1)
  }
}

const getTypeHint = (type: string): string => {
  const hints = {
    static: '固定字符串或数字',
    function: '如: now(), userId()',
    expression: '如: user.id + "_" + system.timestamp',
    template: '如: {{user.username}}_{{system.date}}'
  }
  return hints[type as keyof typeof hints] || ''
}

const getTypePlaceholder = (type: string): string => {
  const placeholders = {
    static: '输入固定值',
    function: 'now()',
    expression: 'user.id + "_suffix"',
    template: '{{user.username}}_{{system.date}}'
  }
  return placeholders[type as keyof typeof placeholders] || ''
}



const showFunctionHelper = (index: number) => {
  currentEditingIndex.value = index
  showFunctionModal.value = true
}

// 获取函数信息
const getFunctionInfo = (functionCall: string) => {
  if (!functionCall) return null

  // 解析函数名
  const match = functionCall.match(/^(\w+)\(/)
  if (!match) return null

  const functionName = match[1]
  const functions = dynamicParamResolver.getBuiltinFunctions()
  const func = functions.find(f => f.name === functionName)

  // 确保函数存在，并且如果没有参数，返回一个空参数数组
  if (func) {
    if (!func.parameters) {
      func.parameters = []
    }
    return func
  }

  return null
}

// 解析函数调用中的参数值
const parseFunctionCallParams = (functionCall: string): Record<string, any> => {
  const match = functionCall.match(/^(\w+)\((.*)\)$/)
  if (!match) return {}

  const [, functionName, argsStr] = match
  const funcInfo = getFunctionInfo(functionCall)
  if (!funcInfo || !funcInfo.parameters) return {}

  const params: Record<string, any> = {}

  if (argsStr.trim()) {
    const args = argsStr.split(',').map(arg => {
      const trimmed = arg.trim()

      // 数字
      if (/^\d+$/.test(trimmed)) {
        return parseInt(trimmed, 10)
      } else if (/^\d+\.\d+$/.test(trimmed)) {
        return parseFloat(trimmed)
      }
      // 字符串（带引号）
      else if (/^["'].*["']$/.test(trimmed)) {
        return trimmed.slice(1, -1)
      }
      // 布尔值
      else if (trimmed === 'true') {
        return true
      } else if (trimmed === 'false') {
        return false
      }
      // 默认作为字符串
      else {
        return trimmed
      }
    })

    // 将解析的参数值映射到参数名
    funcInfo.parameters.forEach((p, i) => {
      if (i < args.length) {
        params[p.name] = args[i]
      }
    })
  }

  return params
}

// 获取函数参数值
const getFunctionParams = (param: DynamicParamConfig, index: number) => {
  if (!functionParams.value[index]) {
    functionParams.value[index] = {}

    // 首先尝试从现有函数调用中解析参数值
    const parsedParams = parseFunctionCallParams(param.value)
    Object.assign(functionParams.value[index], parsedParams)

    // 然后初始化默认值（只对未设置的参数）
    const funcInfo = getFunctionInfo(param.value)
    if (funcInfo && funcInfo.parameters) {
      funcInfo.parameters.forEach(p => {
        if (functionParams.value[index][p.name] === undefined && p.default !== undefined) {
          functionParams.value[index][p.name] = p.default
        }
      })
    }

    console.log('🔍 [DynamicParamsConfig] getFunctionParams for', param.value, ':', functionParams.value[index])
  }
  return functionParams.value[index]
}

// 获取输入框类型
const getInputType = (paramType: string): string => {
  switch (paramType) {
    case 'number':
      return 'number'
    case 'boolean':
      return 'checkbox'
    default:
      return 'text'
  }
}

// 值变化处理
const onValueChange = (param: DynamicParamConfig, index: number) => {
  if (param.type === 'function') {
    // 清空之前的参数配置
    functionParams.value[index] = {}

    // 如果是带参数的函数，初始化参数
    const funcInfo = getFunctionInfo(param.value)
    if (funcInfo && funcInfo.parameters && funcInfo.parameters.length > 0) {
      funcInfo.parameters.forEach(p => {
        if (p.default !== undefined) {
          functionParams.value[index][p.name] = p.default
        }
      })

      // 如果有必需参数，更新函数调用
      updateFunctionCall(param, index)
    }
  }
}

// 更新函数调用
const updateFunctionCall = (param: DynamicParamConfig, index: number) => {
  const funcInfo = getFunctionInfo(param.value)
  if (!funcInfo || !funcInfo.parameters || funcInfo.parameters.length === 0) {
    return
  }

  const params = getFunctionParams(param, index)
  const functionName = funcInfo.name

  // 构建参数列表
  const paramValues = funcInfo.parameters.map(p => {
    const value = params[p.name]
    if (value === undefined || value === '') {
      return p.required ? `<${p.name}>` : (p.default !== undefined ? p.default : '')
    }

    // 根据类型格式化参数值
    if (p.type === 'string') {
      return `"${value}"`
    } else if (p.type === 'number') {
      return Number(value)
    } else if (p.type === 'boolean') {
      return Boolean(value)
    }

    return value
  }).filter(v => v !== '')

  // 更新函数调用字符串
  param.value = `${functionName}(${paramValues.join(', ')})`
}

// 获取数组元素函数参数值
const getArrayElementFunctionParams = (paramIndex: number, elementIndex: number) => {
  const key = `${paramIndex}-${elementIndex}`
  if (!arrayElementFunctionParams.value[key]) {
    arrayElementFunctionParams.value[key] = {}

    // 首先尝试从现有函数调用中解析参数值
    const param = localParams.value[paramIndex]
    if (param.arrayElements && param.arrayElements[elementIndex]) {
      const element = param.arrayElements[elementIndex]
      const parsedParams = parseFunctionCallParams(element.value)
      Object.assign(arrayElementFunctionParams.value[key], parsedParams)

      // 然后初始化默认值（只对未设置的参数）
      const funcInfo = getFunctionInfo(element.value)
      if (funcInfo && funcInfo.parameters) {
        funcInfo.parameters.forEach(p => {
          if (arrayElementFunctionParams.value[key][p.name] === undefined && p.default !== undefined) {
            arrayElementFunctionParams.value[key][p.name] = p.default
          }
        })
      }

      console.log('🔍 [DynamicParamsConfig] getArrayElementFunctionParams for', element.value, ':', arrayElementFunctionParams.value[key])
    }
  }
  return arrayElementFunctionParams.value[key]
}

// 更新数组元素函数调用
const updateArrayElementFunctionCall = (paramIndex: number, elementIndex: number) => {
  const param = localParams.value[paramIndex]
  if (!param.arrayElements || !param.arrayElements[elementIndex]) {
    return
  }

  const element = param.arrayElements[elementIndex]
  const funcInfo = getFunctionInfo(element.value)
  if (!funcInfo || !funcInfo.parameters || funcInfo.parameters.length === 0) {
    return
  }

  const params = getArrayElementFunctionParams(paramIndex, elementIndex)
  const functionName = funcInfo.name

  // 构建参数列表
  const paramValues = funcInfo.parameters.map(p => {
    const value = params[p.name]
    if (value === undefined || value === '') {
      return p.required ? `<${p.name}>` : (p.default !== undefined ? p.default : '')
    }

    // 根据类型格式化参数值
    if (p.type === 'string') {
      return `"${value}"`
    } else if (p.type === 'number') {
      return Number(value)
    } else if (p.type === 'boolean') {
      return Boolean(value)
    }

    return value
  }).filter(v => v !== '')

  // 更新函数调用字符串
  element.value = `${functionName}(${paramValues.join(', ')})`
}

// 刷新所有参数的预览值
const refreshAllPreviews = async () => {
  for (let i = 0; i < localParams.value.length; i++) {
    const param = localParams.value[i]
    if (param.enabled && param.key && param.value) {
      await refreshPreview(i)
    }
  }
}

// 打开函数选择模态框
const openFunctionModal = (index: number) => {
  currentEditingIndex.value = index
  currentEditingArrayElement.value = null
  showFunctionModal.value = true
}

// 为数组元素打开函数选择模态框
const openFunctionModalForArrayElement = (paramIndex: number, elementIndex: number) => {
  currentEditingIndex.value = -1
  currentEditingArrayElement.value = { paramIndex, elementIndex }
  showFunctionModal.value = true
}

const selectFunction = (functionCall: string) => {
  if (currentEditingIndex.value >= 0) {
    // 为主参数选择函数
    const param = localParams.value[currentEditingIndex.value]
    const index = currentEditingIndex.value

    param.value = functionCall
    param.type = 'function'

    // 初始化函数参数
    onValueChange(param, index)

    // 自动刷新预览
    refreshPreview(index)
  } else if (currentEditingArrayElement.value) {
    // 为数组元素选择函数
    const { paramIndex, elementIndex } = currentEditingArrayElement.value
    const param = localParams.value[paramIndex]

    if (param.arrayElements && param.arrayElements[elementIndex]) {
      const element = param.arrayElements[elementIndex]
      element.value = functionCall
      element.type = 'function'

      // 初始化数组元素函数参数
      const key = `${paramIndex}-${elementIndex}`
      arrayElementFunctionParams.value[key] = {}

      // 如果是带参数的函数，初始化参数
      const funcInfo = getFunctionInfo(functionCall)
      if (funcInfo && funcInfo.parameters && funcInfo.parameters.length > 0) {
        funcInfo.parameters.forEach(p => {
          if (p.default !== undefined) {
            arrayElementFunctionParams.value[key][p.name] = p.default
          }
        })

        // 如果有必需参数，更新函数调用
        updateArrayElementFunctionCall(paramIndex, elementIndex)
      }
    }
  }

  showFunctionModal.value = false
  currentEditingIndex.value = -1
  currentEditingArrayElement.value = null
}

// 刷新预览值
const refreshPreview = async (index: number) => {
  const param = localParams.value[index]
  if (!param || !param.enabled || !param.key || !param.value) {
    previewValues.value[index] = '请配置完整的参数信息'
    return
  }

  previewLoading.value[index] = true

  try {
    const value = await dynamicParamResolver.resolveValue(param)

    // 格式化显示值
    if (value === null || value === undefined) {
      previewValues.value[index] = 'null'
    } else if (typeof value === 'object') {
      previewValues.value[index] = JSON.stringify(value, null, 2)
    } else if (typeof value === 'string') {
      previewValues.value[index] = `"${value}"`
    } else {
      previewValues.value[index] = String(value)
    }
  } catch (error) {
    previewValues.value[index] = `错误: ${error instanceof Error ? error.message : String(error)}`
  } finally {
    previewLoading.value[index] = false
  }
}

// 组件挂载后初始化函数参数
onMounted(() => {
  console.log('🔍 [DynamicParamsConfig] onMounted - initializing function params')

  // 初始化函数参数
  props.modelValue.forEach((param, index) => {
    if (param.type === 'function') {
      // 触发函数参数初始化
      getFunctionParams(param, index)
    }

    // 处理数组元素中的函数
    if (param.type === 'array' && param.arrayElements) {
      param.arrayElements.forEach((element, elementIndex) => {
        if (element.type === 'function') {
          // 触发数组元素函数参数初始化
          getArrayElementFunctionParams(index, elementIndex)
        }
      })
    }
  })

  console.log('🔍 [DynamicParamsConfig] onMounted - function params initialized:', functionParams.value)
})

</script>
