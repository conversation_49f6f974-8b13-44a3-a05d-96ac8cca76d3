<template>
  <div class="space-y-6">
    <!-- 导出基础配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        导出基础配置
      </h4>
      
      <div class="space-y-4">
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text">启用导出功能</span>
            <input 
              v-model="localExportConfig.enabled"
              type="checkbox" 
              class="toggle toggle-primary"
            />
          </label>
        </div>

        <div v-if="localExportConfig.enabled" class="space-y-4">
          <!-- 支持的导出格式（固定显示） -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">支持的导出格式</span>
            </label>
            <div class="flex flex-wrap gap-4">
              <div class="badge badge-primary">Excel (.xlsx)</div>
              <div class="badge badge-primary">CSV (.csv)</div>
              <div class="badge badge-primary">PDF (.pdf)</div>
            </div>
            <div class="label">
              <span class="label-text-alt">固定支持三种格式，无需配置</span>
            </div>
          </div>

          <!-- 导出接口配置 -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">导出接口地址 *</span>
            </label>
            <input
              v-model="apiConfig.url"
              type="text"
              class="input input-bordered"
              placeholder="/api/export"
              required
            />
          </div>

          <!-- 请求方式提示 -->
          <div class="form-control">
            <label class="label">
              <span class="label-text-alt text-base-content/60">
                默认使用 POST 请求，Content-Type: application/json
              </span>
            </label>
          </div>

          <!-- 文件名模板 -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">文件名模板</span>
              <span class="label-text-alt">支持变量: {name}, {timestamp}, {date}</span>
            </label>
            <input 
              v-model="localExportConfig.filename"
              type="text" 
              class="input input-bordered"
              placeholder="{name}_{timestamp}"
            />
            <div class="label">
              <span class="label-text-alt">预览: {{ filenamePreview }}</span>
            </div>
          </div>


        </div>
      </div>
    </div>

    <!-- 导出预览 -->
    <div v-if="localExportConfig.enabled" class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4">导出预览</h4>
      
      <div class="space-y-2">
        <div class="text-sm">
          <span class="font-medium">导出格式:</span>
          <span class="ml-2">xlsx, csv, pdf</span>
        </div>

        <div class="text-sm">
          <span class="font-medium">导出接口:</span>
          <span class="ml-2">POST {{ localExportConfig.api?.url || '/api/export' }}</span>
        </div>

        <div class="text-sm">
          <span class="font-medium">文件名:</span>
          <span class="ml-2">{{ filenamePreview }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { ExportConfig } from '@/types/lowcode'
import { formatDate } from '@/utils/lowcode'

// Props
const props = defineProps<{
  modelValue: ExportConfig
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: ExportConfig]
}>()

// 本地状态
const localExportConfig = ref<ExportConfig>(JSON.parse(JSON.stringify(props.modelValue)))

// API配置
const apiConfig = computed({
  get: () => localExportConfig.value.api || { url: '/api/export', method: 'POST' },
  set: (value) => {
    localExportConfig.value.api = value
  }
})



// 计算属性
const filenamePreview = computed(() => {
  const now = new Date()
  const timestamp = formatDate(now, 'YYYYMMDDHHmmss')
  const date = formatDate(now, 'YYYY-MM-DD')
  
  return localExportConfig.value.filename
    .replace('{name}', '示例配置')
    .replace('{timestamp}', timestamp)
    .replace('{date}', date)
})

// 监听变化
watch(localExportConfig, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  // 避免递归更新：只有当值真正不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(localExportConfig.value)) {
    localExportConfig.value = JSON.parse(JSON.stringify(newValue))
  }
}, { deep: true })



// 监听API配置变化
watch(apiConfig, (newValue) => {
  // 确保method始终为POST，但避免递归更新
  const newApiConfig = {
    ...newValue,
    method: 'POST' as const
  }

  // 只有当API配置真正不同时才更新
  if (JSON.stringify(newApiConfig) !== JSON.stringify(localExportConfig.value.api)) {
    localExportConfig.value.api = newApiConfig
  }
}, { deep: true })



// 方法


</script>
