<template>
  <div class="space-y-6">
    <!-- 工具栏 -->
    <div class="flex items-center justify-between">
      <h4 class="font-semibold flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
        搜索字段配置
        <span class="badge badge-primary badge-sm">{{ enabledFieldsCount }}</span>
      </h4>
      
      <div class="flex gap-2">
        <button
          class="btn btn-outline btn-sm"
          @click="addSearchField"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
          添加字段
        </button>
      </div>
    </div>

    <!-- 字段列表 -->
    <div class="bg-base-200 rounded-lg p-4">
      <div v-if="localSearchConfig.fields.length === 0" class="text-center py-8">
        <svg class="w-12 h-12 mx-auto text-base-content/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
        <p class="text-base-content/60">暂无搜索字段，点击"添加字段"开始配置</p>
      </div>

      <div v-else>
        <Draggable
          v-model="localSearchConfig.fields"
          item-key="id"
          handle=".drag-handle"
          class="space-y-4"
          @end="onDragEnd"
        >
          <template #item="{ element: field, index }">
            <div 
              class="bg-base-100 rounded-lg p-4 border"
              :class="{ 'border-primary': field.enabled, 'border-base-300 opacity-60': !field.enabled }"
            >
              <div class="flex items-center gap-4">
                <!-- 拖拽手柄 -->
                <div class="drag-handle cursor-move text-base-content/40 hover:text-primary">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
                  </svg>
                </div>

                <!-- 启用开关 -->
                <input 
                  v-model="field.enabled"
                  type="checkbox" 
                  class="toggle toggle-primary toggle-sm"
                />

                <!-- 字段信息 -->
                <div class="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div class="form-control">
                    <label class="label label-text-alt">字段标识</label>
                    <input 
                      v-model="field.key"
                      type="text" 
                      class="input input-bordered input-sm"
                      placeholder="fieldKey"
                    />
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">显示名称</label>
                    <input 
                      v-model="field.label"
                      type="text" 
                      class="input input-bordered input-sm"
                      placeholder="字段名称"
                    />
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">字段类型</label>
                    <select 
                      v-model="field.type"
                      class="select select-bordered select-sm"
                    >
                      <option value="input">文本输入</option>
                      <option value="select">下拉选择</option>
                      <option value="date">日期选择</option>
                      <option value="daterange">日期范围</option>
                      <option value="number">数字输入</option>
                      <option value="textarea">多行文本</option>
                    </select>
                  </div>
                  
                  <div class="form-control">
                    <label class="label label-text-alt">占位符</label>
                    <input
                      v-model="field.placeholder"
                      type="text"
                      class="input input-bordered input-sm"
                      placeholder="请输入..."
                    />
                  </div>

                  <!-- 日期字段的时间选择 -->
                  <div v-if="field.type === 'date'" class="form-control">
                    <label class="label label-text-alt">时间选择</label>
                    <select
                      v-model="field.timeOption"
                      class="select select-bordered select-sm"
                    >
                      <option value="">默认</option>
                      <option value="earliest">最早时间 (00:00:00)</option>
                      <option value="latest">最晚时间 (23:59:59)</option>
                    </select>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-2">
                  <button 
                    class="btn btn-ghost btn-sm"
                    @click="editField(index)"
                    title="编辑"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                  </button>
                  
                  <button 
                    class="btn btn-ghost btn-sm text-error"
                    @click="removeField(index)"
                    title="删除"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 高级配置 -->
              <div v-if="field.type === 'select'" class="mt-4 pt-4 border-t">
                <label class="label label-text-alt">选项配置</label>
                <div class="space-y-2">
                  <div 
                    v-for="(option, optionIndex) in field.options || []" 
                    :key="optionIndex"
                    class="flex gap-2"
                  >
                    <input 
                      v-model="option.label"
                      type="text" 
                      class="input input-bordered input-sm flex-1"
                      placeholder="显示文本"
                    />
                    <input 
                      v-model="option.value"
                      type="text" 
                      class="input input-bordered input-sm flex-1"
                      placeholder="选项值"
                    />
                    <button 
                      class="btn btn-ghost btn-sm text-error"
                      @click="removeOption(field, optionIndex)"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>
                  
                  <button 
                    class="btn btn-outline btn-sm"
                    @click="addOption(field)"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    添加选项
                  </button>
                </div>
              </div>

              <!-- 字段选项 -->
              <div v-if="field.type === 'select'" class="mt-4 pt-4 border-t">
                <div class="space-y-2">
                  <label class="label cursor-pointer">
                    <input
                      v-model="field.multiple"
                      type="checkbox"
                      class="checkbox checkbox-sm"
                    />
                    <span class="label-text ml-2">多选</span>
                  </label>
                </div>
              </div>
            </div>
          </template>
        </Draggable>
      </div>
    </div>

    <!-- 布局配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4">布局配置</h4>


      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text">组件间距</span>
          </label>
          <select v-model="localSearchConfig.layout.gap" class="select select-bordered">
            <option value="8px">紧凑 (8px)</option>
            <option value="12px">较紧凑 (12px)</option>
            <option value="16px">标准 (16px)</option>
            <option value="20px">较宽松 (20px)</option>
            <option value="24px">宽松 (24px)</option>
            <option value="32px">很宽松 (32px)</option>
          </select>
        </div>
      </div>

      <!-- 按钮配置 -->
      <div class="card bg-base-100 p-3">
        <ButtonConfigPanel v-model="localSearchConfig.layout.buttonConfig!" />
      </div>
    </div>

    <!-- 字段编辑模态框 -->
    <FieldEditModal 
      v-model:show="showEditModal"
      v-model:field="editingField"
      @save="saveField"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Draggable from 'vuedraggable'
import type { SearchConfig, SearchFieldConfig, SearchLayoutConfig } from '@/types/lowcode'
import { generateUniqueId } from '@/utils/lowcode'
import FieldEditModal from './FieldEditModal.vue'
import ButtonConfigPanel from './ButtonConfigPanel.vue'

// Props
const props = defineProps<{
  modelValue: SearchConfig
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: SearchConfig]
}>()

// 默认布局配置
const defaultLayoutConfig: SearchLayoutConfig = {
  gap: '16px',
  buttonConfig: {
    position: 'newline',
    alignment: 'start',
    buttons: [
      {
        type: 'search',
        enabled: true
      },
      {
        type: 'reset',
        enabled: true
      },
      {
        type: 'export',
        enabled: true
      }
    ]
  }
}

// 本地状态
const localSearchConfig = ref<SearchConfig>({
  fields: JSON.parse(JSON.stringify(props.modelValue.fields || [])),
  layout: {
    ...defaultLayoutConfig,
    ...props.modelValue.layout,
    buttonConfig: {
      ...defaultLayoutConfig.buttonConfig,
      ...props.modelValue.layout?.buttonConfig,
      buttons: (props.modelValue.layout?.buttonConfig?.buttons?.length || 0) > 0
        ? JSON.parse(JSON.stringify(props.modelValue.layout!.buttonConfig!.buttons))
        : JSON.parse(JSON.stringify(defaultLayoutConfig.buttonConfig!.buttons))
    }
  }
})
const showEditModal = ref(false)
const editingField = ref<SearchFieldConfig | null>(null)
const editingIndex = ref(-1)

// 计算属性
const enabledFieldsCount = computed(() => {
  return localSearchConfig.value.fields.filter(field => field.enabled).length
})

// 防止递归更新的标志
let isUpdatingFromProps = false

// 监听变化
watch(localSearchConfig, (newValue) => {
  if (!isUpdatingFromProps) {
    emit('update:modelValue', newValue)
  }
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  // 避免递归更新：设置标志并比较值
  const currentValue = JSON.stringify(localSearchConfig.value)
  const newValueStr = JSON.stringify({
    fields: newValue.fields || [],
    layout: {
      ...defaultLayoutConfig,
      ...newValue.layout,
      buttonConfig: {
        ...defaultLayoutConfig.buttonConfig,
        ...newValue.layout?.buttonConfig,
        buttons: (newValue.layout?.buttonConfig?.buttons?.length || 0) > 0
          ? newValue.layout!.buttonConfig!.buttons
          : defaultLayoutConfig.buttonConfig!.buttons
      }
    }
  })

  if (currentValue !== newValueStr) {
    isUpdatingFromProps = true
    localSearchConfig.value = {
      fields: JSON.parse(JSON.stringify(newValue.fields || [])),
      layout: {
        ...defaultLayoutConfig,
        ...newValue.layout,
        buttonConfig: {
          ...defaultLayoutConfig.buttonConfig,
          ...newValue.layout?.buttonConfig,
          buttons: (newValue.layout?.buttonConfig?.buttons?.length || 0) > 0
            ? JSON.parse(JSON.stringify(newValue.layout!.buttonConfig!.buttons))
            : JSON.parse(JSON.stringify(defaultLayoutConfig.buttonConfig!.buttons))
        }
      }
    }
    // 在下一个 tick 重置标志
    setTimeout(() => {
      isUpdatingFromProps = false
    }, 0)
  }
}, { deep: true })

// 方法
const addSearchField = () => {
  const newField: SearchFieldConfig = {
    id: generateUniqueId('search'),
    key: '',
    label: '',
    type: 'input',
    placeholder: '',
    enabled: true,
    order: localSearchConfig.value.fields.length
  }

  localSearchConfig.value.fields.push(newField)
}

const removeField = (index: number) => {
  if (confirm('确定要删除这个搜索字段吗？')) {
    localSearchConfig.value.fields.splice(index, 1)
    updateOrder()
  }
}

const editField = (index: number) => {
  editingField.value = { ...localSearchConfig.value.fields[index] }
  editingIndex.value = index
  showEditModal.value = true
}

const saveField = (field: SearchFieldConfig) => {
  if (editingIndex.value >= 0) {
    localSearchConfig.value.fields[editingIndex.value] = field
  }
  showEditModal.value = false
  editingField.value = null
  editingIndex.value = -1
}

const addOption = (field: SearchFieldConfig) => {
  if (!field.options) {
    field.options = []
  }
  field.options.push({ label: '', value: '' })
}

const removeOption = (field: SearchFieldConfig, index: number) => {
  if (field.options) {
    field.options.splice(index, 1)
  }
}

const onDragEnd = () => {
  updateOrder()
}

const updateOrder = () => {
  localSearchConfig.value.fields.forEach((field, index) => {
    field.order = index
  })
}


</script>
