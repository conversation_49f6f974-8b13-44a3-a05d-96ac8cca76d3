<template>
  <div class="space-y-6">
    <!-- 基本信息 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        基本信息
      </h4>
      
      <div class="space-y-4">
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">配置名称 *</span>
          </label>
          <input
            v-model="localConfig.name"
            type="text"
            class="input input-bordered"
            placeholder="请输入配置名称"
            required
          />
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">描述</span>
          </label>
          <textarea
            v-model="localConfig.description"
            class="textarea textarea-bordered"
            placeholder="请输入配置描述"
            rows="3"
          ></textarea>
        </div>

        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">路由路径</span>
            <span class="label-text-alt">用于生成访问链接</span>
          </label>
          <div class="join w-full">
            <input
              v-model="routePathInput"
              type="text"
              class="input input-bordered join-item flex-1"
              placeholder="/runtime/my-page"
              @input="validateRoutePath"
            />
            <button
              type="button"
              class="btn btn-ghost join-item"
              @click="generateRouteFromName"
              title="根据配置名称生成路由路径"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </button>
          </div>
          <div class="label">
            <span class="label-text-alt" :class="routePathError ? 'text-error' : 'text-base-content/60'">
              {{ routePathError || '完整路径: ' + (routePathInput || '[路由路径]') }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据源配置 -->
    <div class="card bg-base-200 p-4">
      <h4 class="font-semibold mb-4 flex items-center gap-2">
        <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
        </svg>
        数据源配置
      </h4>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">API地址 *</span>
              <span class="label-text-alt">
                <div class="dropdown dropdown-end">
                  <div tabindex="0" role="button" class="btn btn-circle btn-ghost btn-xs">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div tabindex="0" class="dropdown-content z-[1] card card-compact w-80 p-2 shadow bg-base-100 border">
                    <div class="card-body">
                      <h3 class="font-bold text-sm">API地址格式说明</h3>
                      <div class="text-xs space-y-1">
                        <p><code>/api/data/list</code> - API路径（直接请求，避免重复前缀）</p>
                        <p><code>api/task-execution/list</code> - API相对路径（避免重复前缀）</p>
                        <p><code>/task-execution/list</code> - 内部路径（自动添加认证）</p>
                        <p><code>data/list</code> - 相对路径（自动添加 /api/ 前缀）</p>
                        <p><code>https://api.example.com/data</code> - 外部URL</p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </label>
            <input
              v-model="localConfig.dataSource.url"
              type="text"
              class="input input-bordered"
              placeholder="/api/data/list"
              required
            />
            <div class="label">
              <span class="label-text-alt text-xs">
                <span v-if="urlType === 'api'" class="text-success">
                  ✓ API路径 - 避免重复前缀，自动添加认证头
                </span>
                <span v-else-if="urlType === 'internal'" class="text-success">
                  ✓ 内部路径 - 直接请求，自动添加认证头
                </span>
                <span v-else-if="urlType === 'relative'" class="text-info">
                  ✓ 相对路径 - 将自动添加 /api/ 前缀和认证头
                </span>
                <span v-else-if="urlType === 'external'" class="text-warning">
                  ⚠ 外部URL - 请确保目标服务器支持跨域
                </span>
              </span>
            </div>
          </div>

          <div class="form-control">
            <label class="label">
              <span class="label-text font-medium">请求方法</span>
            </label>
            <select
              v-model="localConfig.dataSource.method"
              class="select select-bordered"
            >
              <option value="GET">GET</option>
              <option value="POST">POST</option>
            </select>
          </div>
        </div>

        <!-- 响应数据映射 -->
        <div class="collapse collapse-arrow bg-base-100">
          <input type="checkbox" />
          <div class="collapse-title text-sm font-medium">
            响应数据映射配置
          </div>
          <div class="collapse-content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">数据路径</span>
                  <span class="label-text-alt">如: data.list</span>
                </label>
                <input
                  v-model="dataSourceMapping.dataPath"
                  type="text"
                  class="input input-bordered input-sm"
                  placeholder="data.list"
                />
              </div>

              <div class="form-control">
                <label class="label">
                  <span class="label-text">总数路径</span>
                  <span class="label-text-alt">如: data.total</span>
                </label>
                <input
                  v-model="dataSourceMapping.totalPath"
                  type="text"
                  class="input input-bordered input-sm"
                  placeholder="data.total"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 默认请求参数 -->
        <div class="collapse collapse-arrow bg-base-100">
          <input type="checkbox" />
          <div class="collapse-title text-sm font-medium">
            默认请求参数
            <div class="badge badge-info badge-sm ml-2">支持动态参数</div>
          </div>
          <div class="collapse-content">
            <DynamicParamsConfig
              v-model="dynamicParams"
              @update:modelValue="updateDynamicParams"
            />
          </div>
        </div>










      </div>
    </div>




  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { LowCodeConfig, DynamicParamConfig } from '@/types/lowcode'
import DynamicParamsConfig from './DynamicParamsConfig.vue'

// Props
const props = defineProps<{
  modelValue: LowCodeConfig
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: LowCodeConfig]
}>()

// 本地状态
const localConfig = ref({ ...props.modelValue })
const dynamicParams = ref<DynamicParamConfig[]>(props.modelValue.dataSource?.dynamicParams || [])
const routePathInput = ref('')
const routePathError = ref('')

// 计算属性
const urlType = computed(() => {
  const url = localConfig.value.dataSource?.url || ''

  if (url.startsWith('http://') || url.startsWith('https://')) {
    return 'external' // 外部URL
  } else if (url.startsWith('/api/') || url.startsWith('api/')) {
    return 'api' // API路径（避免重复前缀）
  } else if (url.startsWith('/')) {
    return 'internal' // 内部路径
  } else {
    return 'relative' // 相对路径（会自动添加 /api/ 前缀）
  }
})

// 调试日志
console.log('🔍 [BasicConfigPanel] Initial props.modelValue.dataSource.dynamicParams:', props.modelValue.dataSource?.dynamicParams)
console.log('🔍 [BasicConfigPanel] Initial dynamicParams.value:', dynamicParams.value)

// 初始化默认值
const initializeDefaults = () => {
  // 确保 routePath 字段存在，但不覆盖已有值
  if (localConfig.value.routePath === undefined) {
    localConfig.value.routePath = ''
  }

  // 确保 dataSource 存在
  if (!localConfig.value.dataSource) {
    localConfig.value.dataSource = {
      url: '/api/data/list',
      method: 'GET',
      params: {},
      headers: {},
      dynamicParams: [],
      responseMapping: {
        dataPath: 'data.list',
        totalPath: 'data.total'
      }
    }
  }

  // 确保 responseMapping 存在
  if (!localConfig.value.dataSource.responseMapping) {
    localConfig.value.dataSource.responseMapping = {
      dataPath: 'data.list',
      totalPath: 'data.total'
    }
  }

  // 确保 dynamicParams 存在
  if (!localConfig.value.dataSource.dynamicParams) {
    localConfig.value.dataSource.dynamicParams = []
  }
}

// 计算属性
const dataSourceMapping = computed({
  get: () => localConfig.value.dataSource.responseMapping || { dataPath: 'data.list', totalPath: 'data.total' },
  set: (value) => {
    if (!localConfig.value.dataSource.responseMapping) {
      localConfig.value.dataSource.responseMapping = { dataPath: 'data.list' }
    }
    Object.assign(localConfig.value.dataSource.responseMapping, value)
  }
})



// 初始化
initializeDefaults()
// 初始化路由路径输入框
routePathInput.value = localConfig.value.routePath || ''
console.log('🔍 [BasicConfigPanel] Initial routePathInput set to:', routePathInput.value)



// 监听变化并同步到父组件
watch(localConfig, (newValue) => {
  console.log('🔍 [BasicConfigPanel] localConfig changed, routePath:', newValue.routePath)
  emit('update:modelValue', newValue)
}, { deep: true })

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  console.log('🔍 [BasicConfigPanel] props.modelValue changed, routePath:', newValue.routePath)
  // 避免递归更新：只有当值真正不同时才更新
  if (JSON.stringify(newValue) !== JSON.stringify(localConfig.value)) {
    localConfig.value = { ...newValue }
    initializeDefaults()

    // 初始化动态参数
    console.log('🔍 [BasicConfigPanel] watch newValue.dataSource.dynamicParams:', newValue.dataSource?.dynamicParams)
    if (newValue.dataSource?.dynamicParams) {
      dynamicParams.value = [...newValue.dataSource.dynamicParams]
      console.log('🔍 [BasicConfigPanel] dynamicParams.value set to:', dynamicParams.value)
    } else {
      dynamicParams.value = []
      console.log('🔍 [BasicConfigPanel] dynamicParams.value set to empty array')
    }

    // 初始化路由路径 - 在 initializeDefaults 之后设置，确保不被覆盖
    routePathInput.value = localConfig.value.routePath || ''
    console.log('🔍 [BasicConfigPanel] routePathInput initialized to:', routePathInput.value)
  }
}, { immediate: true })

// 监听路由路径输入变化
watch(routePathInput, (newValue) => {
  console.log('🔍 [BasicConfigPanel] routePathInput changed:', newValue)
  // 实时更新配置，确保用户输入能够保存
  localConfig.value.routePath = newValue
  console.log('🔍 [BasicConfigPanel] localConfig.routePath updated:', localConfig.value.routePath)
  // 验证路径格式
  validateRoutePath()
})

// 方法
const updateDynamicParams = (params: DynamicParamConfig[]) => {
  dynamicParams.value = params
  localConfig.value.dataSource.dynamicParams = params

  // 清除旧的静态参数配置，因为现在统一使用动态参数
  if (localConfig.value.dataSource.params) {
    delete localConfig.value.dataSource.params
  }
}

const validateRoutePath = () => {
  const value = routePathInput.value.trim()

  // 清除错误
  routePathError.value = ''

  if (!value) {
    return
  }

  // 路由路径验证规则 - 必须以/开头的完整路径
  if (!value.startsWith('/')) {
    routePathError.value = '路由路径必须以 / 开头'
    return
  }

  // 验证路径格式
  const routePathRegex = /^\/[a-zA-Z0-9\-_\/]*$/

  if (!routePathRegex.test(value)) {
    routePathError.value = '路由路径只能包含字母、数字、连字符、下划线和斜杠'
    return
  }

  if (value.length > 100) {
    routePathError.value = '路由路径长度不能超过100个字符'
    return
  }

  // 验证通过，没有错误
}

const generateRouteFromName = () => {
  const name = localConfig.value.name.trim()
  if (!name) {
    return
  }

  // 将中文和特殊字符转换为合适的路由路径
  let routePath = name
    .toLowerCase()
    // 替换中文字符为拼音或英文（简化处理）
    .replace(/[\u4e00-\u9fa5]/g, '') // 移除中文字符
    // 替换空格和特殊字符为连字符
    .replace(/[^a-zA-Z0-9]/g, '-')
    // 移除连续的连字符
    .replace(/-+/g, '-')
    // 移除开头和结尾的连字符
    .replace(/^-|-$/g, '')

  // 如果处理后为空，使用默认值
  if (!routePath) {
    routePath = 'page-' + Date.now().toString(36)
  }

  // 限制长度
  if (routePath.length > 50) {
    routePath = routePath.substring(0, 50).replace(/-$/, '')
  }

  // 添加前缀，生成完整路径
  routePathInput.value = '/runtime/' + routePath
  validateRoutePath()
}




</script>
