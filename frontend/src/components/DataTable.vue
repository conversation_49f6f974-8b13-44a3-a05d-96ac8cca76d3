<template>
  <div class="overflow-x-auto">
    <table class="table table-zebra w-full">
      <thead>
        <tr>
          <th v-for="column in (columns || [])" :key="column.key" :style="{ width: column.width ? `${column.width}px` : 'auto' }">
            <div class="flex items-center gap-2">
              {{ column.title }}
              <div v-if="column.sortable" class="flex flex-col">
                <button
                  @click="handleSort(column.key, 'asc')"
                  class="btn btn-ghost btn-xs p-0 h-2"
                  :class="{ 'text-primary': sortField === column.key && sortOrder === 'asc' }"
                >
                  ▲
                </button>
                <button
                  @click="handleSort(column.key, 'desc')"
                  class="btn btn-ghost btn-xs p-0 h-2"
                  :class="{ 'text-primary': sortField === column.key && sortOrder === 'desc' }"
                >
                  ▼
                </button>
              </div>
            </div>
          </th>
          <th v-if="$slots.actions">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="loading">
          <td :colspan="(columns?.length || 0) + ($slots.actions ? 1 : 0)" class="text-center py-8">
            <span class="loading loading-spinner loading-md"></span>
            <span class="ml-2">加载中...</span>
          </td>
        </tr>
        <tr v-else-if="!data || data.length === 0">
          <td :colspan="(columns?.length || 0) + ($slots.actions ? 1 : 0)" class="text-center py-8 text-base-content/60">
            暂无数据
          </td>
        </tr>
        <tr v-else v-for="(item, index) in (data || [])" :key="index">
          <td v-for="column in (columns || [])" :key="column.key">
            <span v-if="column.render">
              {{ column.render(item[column.key], item) }}
            </span>
            <span v-else>
              {{ item[column.key] }}
            </span>
          </td>
          <td v-if="$slots.actions">
            <slot name="actions" :item="item" :index="index"></slot>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import type { TableColumn } from '@/types'

interface Props {
  columns: TableColumn[]
  data: any[]
  loading?: boolean
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

interface Emits {
  (e: 'sort', field: string, order: 'asc' | 'desc'): void
}

const props = withDefaults(defineProps<Props>(), {
  columns: () => [],
  data: () => [],
  loading: false,
  sortField: '',
  sortOrder: 'asc'
})

const emit = defineEmits<Emits>()

const handleSort = (field: string, order: 'asc' | 'desc') => {
  emit('sort', field, order)
}
</script>
