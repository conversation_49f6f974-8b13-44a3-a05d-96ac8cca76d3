<template>
  <div class="card bg-base-100 shadow-sm mb-6">
    <div class="card-body">
      <h3 class="card-title text-lg mb-4">搜索条件</h3>
      
      <form @submit.prevent="handleSearch" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <div v-for="field in fields" :key="field.key" class="form-control">
          <label class="label">
            <span class="label-text">{{ field.label }}</span>
          </label>
          
          <!-- 输入框 -->
          <input
            v-if="field.type === 'input'"
            v-model="formData[field.key]"
            type="text"
            :placeholder="field.placeholder"
            class="input input-bordered input-sm"
          />
          
          <!-- 数字输入框 -->
          <input
            v-else-if="field.type === 'number'"
            v-model.number="formData[field.key]"
            type="number"
            :placeholder="field.placeholder"
            class="input input-bordered input-sm"
          />
          
          <!-- 下拉选择 -->
          <select
            v-else-if="field.type === 'select'"
            v-model="formData[field.key]"
            class="select select-bordered select-sm"
          >
            <option value="">{{ field.placeholder || '请选择' }}</option>
            <option v-for="option in field.options" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
          
          <!-- 日期范围 -->
          <div v-else-if="field.type === 'daterange'" class="flex gap-2">
            <input
              v-model="formData[field.key + 'Begin']"
              type="datetime-local"
              class="input input-bordered input-sm flex-1"
            />
            <input
              v-model="formData[field.key + 'End']"
              type="datetime-local"
              class="input input-bordered input-sm flex-1"
            />
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="form-control flex-row gap-2 items-end">
          <button type="submit" class="btn btn-primary btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            搜索
          </button>
          <button type="button" @click="handleReset" class="btn btn-ghost btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            重置
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import type { FormField } from '@/types'

interface Props {
  fields: FormField[]
  modelValue: Record<string, any>
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'search', value: Record<string, any>): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formData = reactive({ ...props.modelValue })

// 监听表单数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue)
}, { deep: true })

const handleSearch = () => {
  // 处理日期范围字段，转换为秒级时间戳
  const searchData = { ...formData }

  props.fields.forEach(field => {
    if (field.type === 'daterange') {
      const beginKey = field.key + 'Begin'
      const endKey = field.key + 'End'

      if (searchData[beginKey]) {
        // 转换为秒级时间戳
        searchData[beginKey] = Math.floor(new Date(searchData[beginKey]).getTime() / 1000)
      }
      if (searchData[endKey]) {
        // 转换为秒级时间戳
        searchData[endKey] = Math.floor(new Date(searchData[endKey]).getTime() / 1000)
      }
    }
  })

  emit('search', searchData)
}

const handleReset = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
  emit('reset')
}
</script>
