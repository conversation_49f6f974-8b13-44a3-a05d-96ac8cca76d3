<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 transform translate-y-2 scale-95"
      enter-to-class="opacity-100 transform translate-y-0 scale-100"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 transform translate-y-0 scale-100"
      leave-to-class="opacity-0 transform translate-y-2 scale-95"
    >
      <div
        v-if="visible"
        class="fixed top-4 right-4 z-50 max-w-sm w-full"
        role="alert"
      >
        <div
          class="alert shadow-lg border-0"
          :class="alertClasses"
        >
          <!-- 图标 -->
          <div class="flex-shrink-0">
            <component :is="iconComponent" class="w-6 h-6" />
          </div>
          
          <!-- 内容 -->
          <div class="flex-1 min-w-0">
            <div v-if="title" class="font-medium text-sm">{{ title }}</div>
            <div class="text-sm" :class="{ 'mt-1': title }">{{ message }}</div>
          </div>
          
          <!-- 关闭按钮 -->
          <button
            v-if="closable"
            @click="close"
            class="btn btn-sm btn-circle btn-ghost ml-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, h } from 'vue'

interface ToastProps {
  type?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  onClose?: () => void
}

// 导出类型
export type { ToastProps }

const props = withDefaults(defineProps<ToastProps>(), {
  type: 'info',
  duration: 3000,
  closable: true
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)

// 计算样式类
const alertClasses = computed(() => {
  const baseClasses = 'alert'
  switch (props.type) {
    case 'success':
      return `${baseClasses} alert-success`
    case 'error':
      return `${baseClasses} alert-error`
    case 'warning':
      return `${baseClasses} alert-warning`
    case 'info':
    default:
      return `${baseClasses} alert-info`
  }
})

// 计算图标组件
const iconComponent = computed(() => {
  switch (props.type) {
    case 'success':
      return h('svg', {
        class: 'w-6 h-6',
        fill: 'none',
        stroke: 'currentColor',
        viewBox: '0 0 24 24'
      }, [
        h('path', {
          'stroke-linecap': 'round',
          'stroke-linejoin': 'round',
          'stroke-width': '2',
          d: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
        })
      ])
    case 'error':
      return h('svg', {
        class: 'w-6 h-6',
        fill: 'none',
        stroke: 'currentColor',
        viewBox: '0 0 24 24'
      }, [
        h('path', {
          'stroke-linecap': 'round',
          'stroke-linejoin': 'round',
          'stroke-width': '2',
          d: 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
        })
      ])
    case 'warning':
      return h('svg', {
        class: 'w-6 h-6',
        fill: 'none',
        stroke: 'currentColor',
        viewBox: '0 0 24 24'
      }, [
        h('path', {
          'stroke-linecap': 'round',
          'stroke-linejoin': 'round',
          'stroke-width': '2',
          d: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z'
        })
      ])
    case 'info':
    default:
      return h('svg', {
        class: 'w-6 h-6',
        fill: 'none',
        stroke: 'currentColor',
        viewBox: '0 0 24 24'
      }, [
        h('path', {
          'stroke-linecap': 'round',
          'stroke-linejoin': 'round',
          'stroke-width': '2',
          d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
        })
      ])
  }
})

// 关闭方法
const close = () => {
  visible.value = false
  emit('close')
  props.onClose?.()
}

// 自动关闭
let timer: ReturnType<typeof setTimeout> | null = null

const startTimer = () => {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close()
    }, props.duration)
  }
}

const clearTimer = () => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
}

// 组件挂载时显示
onMounted(() => {
  visible.value = true
  startTimer()
})

// 鼠标悬停时暂停计时器
const handleMouseEnter = () => {
  clearTimer()
}

const handleMouseLeave = () => {
  startTimer()
}
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
