<template>
  <dialog 
    ref="modalRef" 
    class="modal"
    :class="modalClasses"
    @close="handleClose"
  >
    <div class="modal-box" :class="sizeClasses" :style="customStyles">
      <!-- 模态框内容 -->
      <div class="flex items-start gap-3 mb-6">
        <!-- 图标 -->
        <div class="flex-shrink-0">
          <component 
            :is="iconComponent" 
            class="w-8 h-8"
            :class="iconClasses"
          />
        </div>
        
        <!-- 文本内容 -->
        <div class="flex-1 min-w-0">
          <h3 class="text-lg font-bold text-base-content">{{ config.title }}</h3>
          
          <p v-if="config.subtitle" class="text-sm text-base-content/70 mt-1">
            {{ config.subtitle }}
          </p>
          
          <p v-if="config.description" class="text-sm mt-2" :class="descriptionClasses">
            {{ config.description }}
          </p>
          
          <!-- 自定义内容插槽 -->
          <div v-if="$slots.default" class="mt-3">
            <slot />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <form method="dialog" class="flex gap-3">
          <button 
            v-for="(button, index) in config.buttons" 
            :key="index"
            type="button"
            class="btn"
            :class="getButtonClasses(button)"
            :disabled="button.disabled || button.loading"
            @click="handleButtonClick(index, button)"
          >
            <!-- 加载图标 -->
            <span v-if="button.loading" class="loading loading-spinner loading-sm"></span>
            
            <!-- 按钮图标 -->
            <component 
              v-else-if="getButtonIcon(button, index)" 
              :is="getButtonIcon(button, index)"
              class="w-4 h-4"
            />
            
            {{ button.text }}
          </button>
        </form>
      </div>
    </div>
    
    <!-- 背景遮罩 -->
    <form 
      v-if="config.closable !== false" 
      method="dialog" 
      class="modal-backdrop"
    >
      <button type="button" @click="handleBackdropClick">关闭</button>
    </form>
  </dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, h } from 'vue'
import type { ModalConfig, ModalEvents, ModalButton } from '@/types/modal'

// Props
interface Props {
  show: boolean
  config: ModalConfig
}

const props = withDefaults(defineProps<Props>(), {
  show: false
})

// Emits
const emit = defineEmits<ModalEvents>()

// Refs
const modalRef = ref<HTMLDialogElement>()

// 计算属性
const modalClasses = computed(() => ({
  'modal-open': props.show
}))

const sizeClasses = computed(() => {
  const sizeMap = {
    sm: 'max-w-sm',
    md: 'max-w-md', 
    lg: 'max-w-lg',
    xl: 'max-w-xl'
  }
  return sizeMap[props.config.size || 'md']
})

const customStyles = computed(() => {
  const styles: Record<string, string> = {}
  if (props.config.maxWidth) {
    styles.maxWidth = props.config.maxWidth
  }
  return styles
})

const iconClasses = computed(() => {
  const typeMap = {
    warning: 'text-warning',
    danger: 'text-error', 
    info: 'text-info',
    success: 'text-success'
  }
  return typeMap[props.config.type]
})

const descriptionClasses = computed(() => {
  const typeMap = {
    warning: 'text-warning',
    danger: 'text-error',
    info: 'text-info', 
    success: 'text-success'
  }
  return typeMap[props.config.type]
})

// 图标组件
const iconComponent = computed(() => {
  if (props.config.customIcon) {
    // 如果有自定义图标，这里可以扩展支持
    return 'div'
  }
  
  const iconMap = {
    warning: () => h('svg', {
      fill: 'none',
      stroke: 'currentColor', 
      viewBox: '0 0 24 24'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round', 
        'stroke-width': '2',
        d: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'
      })
    ]),
    danger: () => h('svg', {
      fill: 'none',
      stroke: 'currentColor',
      viewBox: '0 0 24 24'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2', 
        d: 'M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
      })
    ]),
    info: () => h('svg', {
      fill: 'none',
      stroke: 'currentColor',
      viewBox: '0 0 24 24'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
      })
    ]),
    success: () => h('svg', {
      fill: 'none', 
      stroke: 'currentColor',
      viewBox: '0 0 24 24'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
      })
    ])
  }
  
  return iconMap[props.config.type]
})

// 方法
const getButtonClasses = (button: ModalButton) => {
  const variantMap = {
    primary: 'btn-primary',
    secondary: 'btn-secondary', 
    error: 'btn-error',
    warning: 'btn-warning',
    info: 'btn-info',
    success: 'btn-success',
    ghost: 'btn-ghost'
  }
  
  const classes = []
  if (button.variant) {
    classes.push(variantMap[button.variant])
  }
  if (button.loading) {
    classes.push('loading')
  }
  
  return classes
}

const getButtonIcon = (button: ModalButton, index: number) => {
  // 为特定按钮类型添加图标
  if (button.variant === 'error' && button.text.includes('删除')) {
    return () => h('svg', {
      fill: 'none',
      stroke: 'currentColor', 
      viewBox: '0 0 24 24'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16'
      })
    ])
  }
  return null
}

const handleButtonClick = async (index: number, button: ModalButton) => {
  emit('button-click', index, button)
  
  // 执行按钮的点击处理函数
  if (button.onClick) {
    await button.onClick()
  }
  
  // 发送特定事件
  if (index === 0 && props.config.buttons?.length === 2) {
    emit('cancel')
  } else if (index === 1 || props.config.buttons?.length === 1) {
    emit('confirm')
  }
}

const handleBackdropClick = () => {
  if (props.config.closable !== false) {
    handleClose()
  }
}

const handleClose = () => {
  emit('close')
  emit('update:show', false)
}

// 监听显示状态
watch(() => props.show, async (newShow) => {
  await nextTick()
  
  if (newShow) {
    modalRef.value?.showModal()
  } else {
    modalRef.value?.close()
  }
}, { immediate: true })

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.config.closable !== false) {
    handleClose()
  }
}

// 生命周期
watch(() => props.show, (show) => {
  if (show) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>
