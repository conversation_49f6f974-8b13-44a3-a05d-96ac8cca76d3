/**
 * 通用组件导出
 */

export { default as BaseModal } from './BaseModal.vue'

// 重新导出相关类型和工具
export type { ModalConfig, ModalButton, ModalType, ModalEvents } from '@/types/modal'
export { useModal } from '@/composables/useModal'
export { 
  modalPresets, 
  createButton, 
  createCancelButton, 
  createConfirmButton, 
  createDeleteButton,
  createModalConfig,
  createConfirmDialog,
  createAlertDialog
} from '@/utils/modal'
