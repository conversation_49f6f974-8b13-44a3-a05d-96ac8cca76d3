/**
 * 低代码配置服务
 */

import { ref, reactive } from 'vue'
import type {
  LowCodeConfig,
  RuntimeState,
  ApiResponse,
  SearchFieldConfig,
  ColumnConfig
} from '@/types/lowcode'
import { lowCodeConfigSchema } from '@/schemas/lowcode-config.schema'
import request from '@/utils/request'

// JSON Schema验证器（简化版）
class SchemaValidator {
  validate(data: any, schema: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // 简化的验证逻辑，实际项目中建议使用 ajv 等专业库
    if (schema.required) {
      for (const field of schema.required) {
        if (!(field in data)) {
          errors.push(`缺少必需字段: ${field}`)
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

const validator = new SchemaValidator()

// 配置管理类
export class LowCodeConfigManager {
  private configs = ref<LowCodeConfig[]>([])
  private currentConfig = ref<LowCodeConfig | null>(null)
  private runtimeState = reactive<RuntimeState>({
    loading: false,
    data: [],
    total: 0,
    currentPage: 1,
    pageSize: 20,
    searchParams: {},
    selectedRows: []
  })

  constructor() {
    // 初始化
  }

  // 获取所有配置
  async getConfigs(params?: { page?: number; pageSize?: number; keyword?: string; routePath?: string; category?: string }): Promise<LowCodeConfig[]> {
    try {
      const queryParams = new URLSearchParams()
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.pageSize) queryParams.append('page_size', params.pageSize.toString())
      if (params?.keyword) queryParams.append('keyword', params.keyword)
      if (params?.routePath) queryParams.append('route_path', params.routePath)
      if (params?.category) queryParams.append('category', params.category)

      const response = await request.get(`/lowcode/configs?${queryParams.toString()}`)

      if (response.data.success) {
        const configs = response.data.data.list.map((item: any) => this.convertFromAPI(item))
        this.configs.value = configs
        return configs
      } else {
        throw new Error(response.data.message || '获取配置失败')
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      // 如果API调用失败，返回空数组
      this.configs.value = []
      return []
    }
  }

  // 刷新配置列表
  async refreshConfigs(params?: { keyword?: string; routePath?: string }): Promise<void> {
    await this.getConfigs(params)
  }

  // 获取单个配置
  async getConfig(id: string): Promise<LowCodeConfig | null> {
    try {
      const response = await request.get(`/lowcode/configs/${id}`)

      if (response.data.success) {
        return this.convertFromAPI(response.data.data)
      } else {
        throw new Error(response.data.message || '获取配置失败')
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      return null
    }
  }

  // 保存配置
  async saveConfig(config: LowCodeConfig): Promise<boolean> {
    console.log('🔍 [lowcode.ts] saveConfig called with routePath:', config.routePath)

    // 验证配置
    const validation = validator.validate(config, lowCodeConfigSchema)
    if (!validation.valid) {
      console.error('配置验证失败:', validation.errors)
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
    }

    try {
      const isUpdate = !!(config.id && config.id.trim())
      let response

      const saveData = {
        name: config.name,
        description: config.description,
        routePath: config.routePath,
        config_data: {
          dataSource: config.dataSource,
          searchConfig: config.searchConfig,
          tableConfig: config.tableConfig,
          exportConfig: config.exportConfig
        }
      }

      console.log('🔍 [lowcode.ts] Saving data with routePath:', saveData.routePath)

      if (isUpdate) {
        // 更新配置
        response = await request.put(`/lowcode/configs/${config.id}`, saveData)
      } else {
        // 创建新配置
        response = await request.post('/lowcode/configs', saveData)
      }

      if (response.data.success) {
        console.log('🔍 [lowcode.ts] Save response data:', response.data.data)
        // 更新本地缓存
        const savedConfig = this.convertFromAPI(response.data.data)
        console.log('🔍 [lowcode.ts] Converted saved config routePath:', savedConfig.routePath)

        if (isUpdate) {
          const index = this.configs.value.findIndex(c => c.id === config.id)
          if (index !== -1) {
            this.configs.value[index] = savedConfig
          }
        } else {
          this.configs.value.push(savedConfig)
        }
        return true
      } else {
        throw new Error(response.data.message || '保存失败')
      }
    } catch (error) {
      console.error('保存配置失败:', error)
      throw error
    }
  }

  // 删除配置
  async deleteConfig(id: string): Promise<boolean> {
    try {
      const response = await request.delete(`/lowcode/configs/${id}`)

      if (response.data.success) {
        // 从本地缓存中移除
        this.configs.value = this.configs.value.filter(c => c.id !== id)
        return true
      } else {
        throw new Error(response.data.message || '删除失败')
      }
    } catch (error) {
      console.error('删除配置失败:', error)
      return false
    }
  }

  // 复制配置
  async cloneConfig(id: string, newName: string): Promise<LowCodeConfig | null> {
    try {
      const response = await request.post(`/lowcode/configs/${id}/clone`, {
        name: newName
      })

      if (response.data.success) {
        const clonedConfig = this.convertFromAPI(response.data.data)
        this.configs.value.push(clonedConfig)
        return clonedConfig
      } else {
        throw new Error(response.data.message || '克隆失败')
      }
    } catch (error) {
      console.error('克隆配置失败:', error)
      return null
    }
  }

  // 从API响应转换为前端配置格式
  private convertFromAPI(apiData: any): LowCodeConfig {
    console.log('🔍 [lowcode.ts] convertFromAPI called with:', apiData)
    console.log('🔍 [lowcode.ts] apiData.routePath:', apiData.routePath)
    console.log('🔍 [lowcode.ts] apiData.config_data?.dataSource?.dynamicParams:', apiData.config_data?.dataSource?.dynamicParams)

    // 兼容多种数据源路径
    const dataSource = apiData.config_data?.dataSource ||
                      apiData.basicConfig?.dataSource ||
                      apiData.config_data?.basicConfig?.dataSource

    // 兼容多种路由路径
    const routePath = apiData.routePath ||
                     apiData.config_data?.basicConfig?.routePath ||
                     ''

    const config: LowCodeConfig = {
      id: apiData.id,
      name: apiData.name,
      description: apiData.description,
      routePath,
      dataSource: (() => {
        if (dataSource) {
          // 确保 dynamicParams 存在且保持原有数据
          return {
            url: dataSource.url || '/api/data/list',
            method: dataSource.method || 'GET',
            params: dataSource.params || {},
            headers: dataSource.headers || {},
            dynamicParams: dataSource.dynamicParams || [],
            responseMapping: dataSource.responseMapping || {
              dataPath: 'data.list',
              totalPath: 'data.total'
            }
          }
        }

        // 默认配置
        return {
          url: '/api/data/list',
          method: 'GET',
          params: {},
          headers: {},
          dynamicParams: [],
          responseMapping: {
            dataPath: 'data.list',
            totalPath: 'data.total'
          }
        }
      })(),
      searchConfig: (() => {
        const defaultLayout = {
          gap: '16px',
          buttonConfig: {
            position: 'newline',
            alignment: 'start',
            buttons: [
              {
                type: 'search',
                enabled: true
              },
              {
                type: 'reset',
                enabled: true
              },
              {
                type: 'export',
                enabled: true
              }
            ]
          }
        }

        // 处理新格式（在 config_data.searchConfig 中）
        if (apiData.config_data?.searchConfig?.fields) {
          return {
            fields: apiData.config_data.searchConfig.fields,
            layout: apiData.config_data.searchConfig.layout || defaultLayout
          }
        }

        // 处理旧格式（在 basicConfig.searchConfig 中）
        if (apiData.basicConfig?.searchConfig?.fields) {
          return {
            fields: apiData.basicConfig.searchConfig.fields,
            layout: apiData.basicConfig.searchConfig.layout || defaultLayout
          }
        }

        // 处理更旧格式（分离的 searchConfig 和 searchLayoutConfig）
        const fields = apiData.config_data?.searchConfig || []
        const layout = apiData.config_data?.searchLayoutConfig || defaultLayout

        return {
          fields,
          layout
        }
      })(),
      tableConfig: (() => {
        const defaultTableConfig = {
          columns: [],
          pagination: {
            pageSize: 20,
            pageSizeOptions: [10, 20, 50, 100],
            pageParamName: 'page',
            pageSizeParamName: 'limit'
          },
          sorting: {
            multiple: false
          },
          features: {
            columnSettings: false,
            fullscreen: false
          },
          rowKey: 'id',
          size: 'medium'
        }

        const apiTableConfig = apiData.config_data?.tableConfig || apiData.basicConfig?.tableConfig
        if (!apiTableConfig) return defaultTableConfig

        // 合并配置，确保新字段有默认值
        return {
          ...defaultTableConfig,
          ...apiTableConfig,
          pagination: {
            ...defaultTableConfig.pagination,
            ...apiTableConfig.pagination,
            // 确保新字段有默认值
            pageParamName: apiTableConfig.pagination?.pageParamName || 'page',
            pageSizeParamName: apiTableConfig.pagination?.pageSizeParamName || 'limit'
          }
        }
      })(),
      exportConfig: apiData.config_data?.exportConfig || apiData.basicConfig?.exportConfig || {
        enabled: false,
        api: {
          url: '/api/export',
          method: 'POST'
        },
        filename: '{name}_{timestamp}',
        columns: []
      },
      metadata: {
        createdBy: apiData.created_by,
        createdAt: apiData.created_at,
        updatedBy: apiData.updated_by,
        updatedAt: apiData.updated_at
      }
    }

    console.log('🔍 [lowcode.ts] convertFromAPI result routePath:', config.routePath)
    // 返回配置
    return config
  }

  // 获取响应式配置列表
  getConfigsRef() {
    return this.configs
  }



  // 设置当前配置
  setCurrentConfig(config: LowCodeConfig | null) {
    this.currentConfig.value = config
  }

  // 获取当前配置
  getCurrentConfig(): LowCodeConfig | null {
    return this.currentConfig.value
  }

  // 获取响应式当前配置
  getCurrentConfigRef() {
    return this.currentConfig
  }

  // 获取运行时状态
  getRuntimeState(): RuntimeState {
    return this.runtimeState
  }

  // 获取响应式运行时状态
  getRuntimeStateRef() {
    return this.runtimeState
  }

  // 更新运行时状态
  updateRuntimeState(updates: Partial<RuntimeState>) {
    Object.assign(this.runtimeState, updates)
  }

  // 生成唯一ID
  private generateId(): string {
    return 'lc_' + Date.now().toString(36) + Math.random().toString(36).substr(2)
  }


}

// 创建全局实例
export const lowCodeConfigManager = new LowCodeConfigManager()

// 导出响应式状态
export const useLowCodeConfig = () => {
  return {
    configs: lowCodeConfigManager.getConfigsRef(),
    currentConfig: lowCodeConfigManager.getCurrentConfigRef(),
    runtimeState: lowCodeConfigManager.getRuntimeStateRef(),

    // 方法
    getConfig: lowCodeConfigManager.getConfig.bind(lowCodeConfigManager),
    saveConfig: lowCodeConfigManager.saveConfig.bind(lowCodeConfigManager),
    deleteConfig: lowCodeConfigManager.deleteConfig.bind(lowCodeConfigManager),
    cloneConfig: lowCodeConfigManager.cloneConfig.bind(lowCodeConfigManager),
    setCurrentConfig: lowCodeConfigManager.setCurrentConfig.bind(lowCodeConfigManager),
    updateRuntimeState: lowCodeConfigManager.updateRuntimeState.bind(lowCodeConfigManager),
    refreshConfigs: lowCodeConfigManager.refreshConfigs.bind(lowCodeConfigManager)
  }
}
