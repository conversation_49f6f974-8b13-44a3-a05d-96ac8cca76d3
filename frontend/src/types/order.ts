// 订单相关类型定义

// 订单查询参数
export interface OrderListQuery {
  orderNo?: string
  routeName?: string
  beginOrEnd?: string // 保留兼容性，标记为废弃
  begin?: string // 新增：起点站查询
  end?: string // 新增：终点站查询
  orderStatus?: number // -1表示全部
  createdTimeBegin?: number // 时间戳
  createdTimeEnd?: number // 时间戳
  vehicleId?: number // -1表示全部
  sortField?: string
  sortOrder?: string
  page?: number
  pageSize?: number
}

// 订单结果数据
export interface OrderResult {
  id: number
  orderNo: string
  routeName: string
  begin: string
  end: string
  status: number
  vehicleId: number
  vehiclePlate?: string
  passengerCount: number
  totalFee: number
  verifiedFee: number
  pendingVerificationFee: number
  refundedFee: number
  createdTime: number
  updatedTime: number
  // 扩展字段
  passengerName?: string
  ticketName?: string
  ticketPrice?: number
  vehicleName?: string
  vinCode?: string
  couponCode?: string
  discountAmount?: number
  actualAmount?: number
  reservedTime?: string
  paymentTime?: number
  cancelTime?: number
}

// 订单分页结果
export interface OrderPageResult {
  data: OrderResult[]
  orderCount: number
  passengerCount: number
  orderTotalFee: number
  verifiedFee: number
  pendingVerificationFee: number
  refundedFee: number
  total?: number
  page?: number
  pageSize?: number
}

// 订单状态枚举
export enum OrderStatus {
  ALL = -1,
  PENDING = 0,
  CONFIRMED = 1,
  CANCELLED = 2,
  COMPLETED = 3,
  REFUNDED = 4
}

// 导出字段定义
export interface ExportField {
  field: string
  label: string
  width?: number
}

// 订单导出请求
export interface ExportOrderRequest extends OrderListQuery {
  fields: ExportField[]
  fileType: 'excel' | 'pdf' | 'csv'
}
