/**
 * 低代码配置相关类型定义
 */

// 基础字段类型
export type FieldType = 'text' | 'number' | 'date' | 'datetime' | 'enum' | 'boolean' | 'custom'

// 搜索字段类型
export type SearchFieldType = 'text' | 'input' | 'select' | 'date' | 'daterange' | 'number' | 'textarea'



// 验证规则
export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
  required?: boolean // 添加required属性
}

// 格式化配置
export interface FormatterConfig {
  type: 'date' | 'number' | 'currency' | 'custom'
  format?: string
  precision?: number
  prefix?: string
  suffix?: string
  customFunction?: string
}

// 数组元素配置
export interface ArrayElementConfig {
  type: 'static' | 'expression' | 'function' | 'template'
  value: string
  description?: string
}

// 动态参数配置
export interface DynamicParamConfig {
  key: string
  type: 'static' | 'expression' | 'function' | 'template' | 'array'
  value: string
  description?: string
  enabled: boolean
  // 数组类型专用配置
  arrayElements?: ArrayElementConfig[]
}





// 数据源配置
export interface DataSourceConfig {
  url: string
  method: 'GET' | 'POST'
  headers?: Record<string, string> // 请求头
  params?: Record<string, any> // 默认请求参数（简单模式）
  dynamicParams?: DynamicParamConfig[] // 动态参数配置（高级模式）
  responseMapping?: {
    dataPath: string // 数据在响应中的路径，如 'data.list'
    totalPath?: string // 总数在响应中的路径，如 'data.total'
  }
}

// 按钮类型
export type ButtonType = 'search' | 'reset' | 'export'

// 按钮配置
export interface ButtonConfig {
  type: ButtonType
  enabled: boolean // 是否启用
  width?: string // 按钮宽度
}

// 搜索字段配置
export interface SearchFieldConfig {
  id: string
  key: string // API参数名
  label: string // 显示标签
  type: SearchFieldType
  placeholder?: string
  options?: Array<{label: string, value: any}>
  defaultValue?: any
  validation?: ValidationRule[]
  enabled: boolean
  order: number
  width?: string // CSS宽度，如 '200px', '20%', 'auto'
  // 特殊配置
  dateFormat?: string // 日期格式
  multiple?: boolean // 多选
  clearable?: boolean // 可清空
  // 日期时间配置
  timeOption?: 'earliest' | 'latest' // 日期字段的时间选择：最早时间(00:00:00) | 最晚时间(23:59:59)
}

// 搜索布局配置
export interface SearchLayoutConfig {
  gap?: string // 组件间距：'8px' | '12px' | '16px' | '20px' | '24px' 等
  // 标签位置固定为顶部，不需要配置
  // 按钮配置
  buttonConfig?: {
    position?: 'inline' | 'newline' // 按钮位置：与字段同行或新行
    alignment?: 'start' | 'center' | 'end' // 按钮对齐方式
    buttons: ButtonConfig[] // 按钮列表
  }
}

// 统一的搜索配置
export interface SearchConfig {
  fields: SearchFieldConfig[] // 搜索字段列表
  layout: SearchLayoutConfig // 布局配置
}

// 表格列配置
export interface ColumnConfig {
  id: string
  key: string // 数据字段名，支持嵌套如 'user.name'
  label: string // 列标题
  title?: string // 列标题别名（兼容性）
  type: FieldType
  width?: number
  minWidth?: number
  sortable: boolean
  resizable: boolean
  formatter?: FormatterConfig
  enumOptions?: Record<string, string>
  enabled: boolean
  order: number
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right' // 固定列
  // 日期格式配置
  dateFormat?: string // 日期格式，如 'YYYY-MM-DD HH:mm:ss'
  customDateFormat?: string // 自定义日期格式
  enumOptionsText?: string // 枚举选项的文本表示（用于编辑）
  // 自定义渲染
  customRender?: {
    type: 'slot' | 'component' | 'function'
    content: string
  }
}

// 表格配置
export interface TableConfig {
  columns: ColumnConfig[]
  pagination: {
    pageSize: number
    pageSizeOptions: number[]
    showSizeChanger?: boolean // 是否显示页大小选择器
    // 新增：分页参数配置
    pageParamName: string // 页码参数名，如 'page'
    pageSizeParamName: string // 页大小参数名，如 'limit' 或 'pageSize'
  }
  sorting: {
    defaultSort?: string
    defaultOrder?: 'asc' | 'desc'
    multiple: boolean // 多列排序
  }
  features: {
    columnSettings: boolean // 已废弃，保留以兼容现有配置
    fullscreen: boolean // 全屏
  }
  rowKey: string // 行唯一标识字段
  size: 'small' | 'medium' | 'large'
}

// 导出配置
export interface ExportConfig {
  enabled: boolean
  // 导出接口配置
  api: {
    url: string // 导出接口地址
    method: 'GET' | 'POST' // 请求方式
  }
  filename: string // 支持模板变量
  columns: string[] // 要导出的列ID
  formats: string[] // 支持的导出格式
}

// 完整的低代码配置
export interface LowCodeConfig {
  id: string
  name: string
  description?: string
  routePath: string
  dataSource: DataSourceConfig
  searchConfig: SearchConfig
  tableConfig: TableConfig
  exportConfig: ExportConfig
  // 元数据
  metadata: {
    createdBy: string
    createdAt: string
    updatedBy: string
    updatedAt: string
  }
}



// 运行时状态
export interface RuntimeState {
  loading: boolean
  data: any[]
  total: number
  currentPage: number
  pageSize: number
  searchParams: Record<string, any>
  sortField?: string
  sortOrder?: 'asc' | 'desc'
  selectedRows: any[]
  error?: string
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  total?: number
  page?: number
  pageSize?: number
}

// 组件注册接口
export interface ComponentRegistry {
  [key: string]: {
    component: any
    props?: Record<string, any>
    description?: string
  }
}

// 事件类型
export type LowCodeEvent = 
  | 'search'
  | 'reset'
  | 'export'
  | 'refresh'
  | 'sort'
  | 'filter'
  | 'select'
  | 'page-change'
  | 'page-size-change'

// 事件处理器
export interface EventHandler {
  type: LowCodeEvent
  handler: (payload: any) => void | Promise<void>
}

// 插件接口
export interface LowCodePlugin {
  name: string
  version: string
  install: (app: any, options?: any) => void
  components?: ComponentRegistry
  validators?: Record<string, (value: any, rule: ValidationRule) => boolean>
  formatters?: Record<string, (value: any, config: FormatterConfig) => string>
}
