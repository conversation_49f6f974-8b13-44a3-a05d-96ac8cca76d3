import request from '@/utils/request'
import type { ApiResponse } from '@/types'

// 设备信息接口
export interface Device {
  id: number
  name: string
  imsi: string
  imei: string
  uid: number
  online: number
  type: number
  // 其他字段根据需要添加
}

// 获取所有设备列表
export const getAllDevices = (params?: {
  uid?: number
  online?: number
  imsi?: string
  type?: number
}): Promise<Device[]> => {
  return request.get<ApiResponse<Device[]>>('/device/all', { params })
    .then(response => response.data.data || [])
}
