import request from '@/utils/request'
import type {
  CaptchaResponse,
  VerifyCaptchaRequest,
  VerifyCaptchaResponse,
  LoginRequest,
  RefreshTokenRequest,
  RefreshTokenResponse,
  LogoutResponse,
  ApiResponse,
  UserInfo
} from '@/types'

// 获取验证码
export const getCaptcha = (): Promise<CaptchaResponse> => {
  return request.get<ApiResponse<{ id: string; image: string }>>('/user/get_captcha')
    .then(response => {
      const data = response.data.data
      console.log('验证码API原始响应:', data)

      if (!data) {
        throw new Error('验证码数据为空')
      }

      return {
        captchaId: data.id,  // 后端返回的是 id，前端期望 captchaId
        image: data.image
      }
    })
}

// 验证验证码
export const verifyCaptcha = (data: VerifyCaptchaRequest): Promise<VerifyCaptchaResponse> => {
  return request.post<ApiResponse<any>>('/user/verify_captcha', data)
    .then(response => {
      // 响应拦截器已经处理了响应，直接返回转换后的格式
      return {
        success: response.data.success,
        message: response.data.message
      }
    })
}

// 用户登录 (v2版本)
export const login = (data: LoginRequest): Promise<{ token: string; refreshToken: string; expiresIn: number; user: UserInfo }> => {
  // 构造后端期望的登录参数，包含验证码ID
  const loginData = {
    username: data.username,
    password: data.password,
    captchaId: data.captchaId  // 添加验证码ID参数
  }

  console.log('发送登录请求:', loginData)

  return request.post<ApiResponse<{ token: string; refreshToken: string; expiresIn: number }>>('/user/v2/login', loginData)
    .then(response => {
      const responseData = response.data.data

      if (!responseData) {
        throw new Error('登录响应数据为空')
      }

      return {
        token: responseData.token,
        refreshToken: responseData.refreshToken,
        expiresIn: responseData.expiresIn,
        user: {
          id: 0, // 临时值，实际应该从其他接口获取用户信息
          username: data.username,
          nickname: data.username,
          email: '',
          phone: '',
          role: '',
          permissions: []
        }
      }
    })
}

// 刷新Token (v2版本)
export const refreshToken = (data: RefreshTokenRequest): Promise<{ token: string; refreshToken: string; expiresIn: number }> => {
  return request.post<ApiResponse<{ token: string; refreshToken: string; expiresIn: number }>>('/user/v2/token/refresh', data)
    .then(response => response.data.data!)
}

// 用户登出 (v2版本)
export const logout = (): Promise<LogoutResponse> => {
  return request.post<ApiResponse<LogoutResponse>>('/user/v2/logout')
    .then(response => response.data.data!)
}

// 获取用户信息
export const getUserInfo = () => {
  return request.get<ApiResponse>('/user/info')
    .then(response => response.data.data)
}
