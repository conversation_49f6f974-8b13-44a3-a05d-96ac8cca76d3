package controllers

import (
	"ccapi/common"
	"ccapi/models/dto"
	"ccapi/pkg"
	"ccapi/server/log"
	"ccapi/server/redis"
	"ccapi/server/websocket"
	"ccapi/service"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	beego "github.com/beego/beego/v2/server/web"
)

// DeviceController 设备模块
type DeviceController struct {
	BaseController
	service             service.DeviceServiceInter
	recordSignalService service.RecordSignalServiceInter
	ipServer            service.IpServiceInter
	userServer          service.UserServiceInter
	hdMapService        service.HdMapServiceInter
	analysisService     service.AnalysisServiceInter
}

// InitDeviceRouter 初始化路由
func InitDeviceRouter() beego.LinkNamespace {
	return beego.NSNamespace("/device",
		beego.NSRouter("/list", &DeviceController{}, "get:List"),                            // 获取设备列表
		beego.NSRouter("/all", &DeviceController{}, "get:All"),                              // 获取所有设备
		beego.NSRouter("/car_list", &DeviceController{}, "get:CarList"),                     // 获取设备列表(无鉴权)
		beego.NSRouter("/create", &DeviceController{}, "post:Create"),                       // 新增设备
		beego.NSRouter("/delete", &DeviceController{}, "post:Delete"),                       // 删除设备
		beego.NSRouter("/detail", &DeviceController{}, "get:Detail"),                        // 设备详情
		beego.NSRouter("/update", &DeviceController{}, "post:Update"),                       // 修改设备
		beego.NSRouter("/get_map", &DeviceController{}, "get:GetMap"),                       // 获取地图
		beego.NSRouter("/get_password", &DeviceController{}, "get:GetPassword"),             // 获取密码
		beego.NSRouter("/analysis", &DeviceController{}, "get:Analysis"),                    // 数据大屏
		beego.NSRouter("/statistics", &DeviceController{}, "get:Statistics"),                // 新版统计接口
		beego.NSRouter("/get_vin", &DeviceController{}, "get:GetVin"),                       // 获取vin码
		beego.NSRouter("/get_imsi", &DeviceController{}, "get:GetImsi"),                     // 获取imsi
		beego.NSRouter("/update_vin", &DeviceController{}, "post:UpdateVin"),                // 更新vin码
		beego.NSRouter("/get_push_config", &DeviceController{}, "get:GetPushConfig"),        // 获取推流配置
		beego.NSRouter("/update_push_config", &DeviceController{}, "post:UpdatePushConfig"), // 修改推流配置
		beego.NSRouter("/push_log", &DeviceController{}, "get:PushLog"),                     // 获取推流记录
		beego.NSRouter("/push", &DeviceController{}, "post:Push"),                           // 开始推流
		beego.NSRouter("/stop", &DeviceController{}, "post:Stop"),                           // 停止推流
	)
}

// Prepare 注册服务
func (c *DeviceController) Prepare() {
	c.service = service.NewDeviceService()
	c.recordSignalService = service.NewRecordSignalService()
	c.ipServer = service.NewIpService()
	c.userServer = service.NewUserService()
	c.hdMapService = service.NewHdMapService()
	c.analysisService = service.NewAnalysisService()
}

// List 获取车辆列表
func (c *DeviceController) List() {
	var query dto.DeviceListQuery
	if err := pkg.UnmarshalQuery(c.GetString, &query); err != nil {
		c.Fail("获取参数异常：" + err.Error())
		return
	}



	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	if query.Uid != -1 {
		userInfo.Id = int64(query.Uid)
	}

	userList, err := c.userServer.Child(userInfo, "")
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	var idsStr []string
	if query.Uid != 1 {
		for _, v := range userList {
			idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
		}
	}

	allId := strings.Join(idsStr, ",")

	// 传递bind_remark到服务层
	data, count, err := c.service.List(c.Ctx, query, allId, userInfo.Id)
	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	nowTs := time.Now().Unix()
	mondayTs := common.GetMondayTs()

	respData := make([]dto.RespDeviceForm, 0)
	for _, value := range data {
		item := c.service.CreateRespDeviceData(mondayTs, nowTs, value)
		respData = append(respData, item)
	}

	c.Success(map[string]interface{}{
		"items": respData,
		"total": count,
	})
	return
}

// CarList 获取车辆列表(无鉴权)
func (c *DeviceController) CarList() {
	var query dto.DeviceListQuery
	if err := pkg.UnmarshalQuery(c.GetString, &query); err != nil {
		c.Fail("获取参数异常：" + err.Error())
		return
	}

	var userInfo dto.UserInfo
	userInfo.Id = 1

	userList, err := c.userServer.Child(userInfo, "")
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	data, count, err := c.service.List(c.Ctx, query, allId, userInfo.Id)
	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	nowTs := time.Now().Unix()
	mondayTs := common.GetMondayTs()

	respData := make([]dto.RespDeviceForm, 0)
	for _, value := range data {
		item := c.service.CreateRespDeviceData(mondayTs, nowTs, value)
		respData = append(respData, item)
	}

	c.Success(map[string]interface{}{
		"items": respData,
		"total": count,
	})
	return
}

// All 获取所有车辆
func (c *DeviceController) All() {
	uid, err := c.GetInt("uid", -1)
	online, err := c.GetInt("online", -1)
	imsi := c.GetString("imsi", "")
	_type, err := c.GetInt("type", -1)

	if err != nil {
		c.Fail("获取参数异常：" + err.Error())
		return
	}

	userInfo, err := c.CheckToken()

	userList, err := c.userServer.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	flag := false
	if userInfo.Id == 1 {
		flag = true
	}

	data, err := c.service.All(uid, imsi, online, allId, flag, _type)

	if err != nil {
		c.Fail("获取列表失败：" + err.Error())
		return
	}

	nowTs := time.Now().Unix()
	mondayTs := common.GetMondayTs()

	respData := make([]dto.RespDeviceForm, 0)
	for _, value := range data {
		item := c.service.CreateRespDeviceData(mondayTs, nowTs, value)
		respData = append(respData, item)
	}

	c.Success(respData)
	return
}

// GetMap 获取地图
func (c *DeviceController) GetMap() {
	id, err := c.GetInt("id", 0)
	if err != nil {
		c.Fail("参数错误")
		return
	}

	device, err := c.service.Find(id)
	if device.Uid == 0 {
		c.Fail("此设备未设置地图")
		return
	}

	user, err := c.userServer.Detail(int64(device.Uid))
	if err != nil {
		c.Fail("获取用户信息失败:" + err.Error())
		return
	}

	// if userInfo.Level == common.XZLevel && id != userInfo.ParentID {
	//	c.Fail("暂无权限")
	//	return
	// }
	//
	// if userInfo.Level == common.XGLevel && (int64(user.ParentId) != userInfo.Id && id != userInfo.Id) {
	//	c.Fail("暂无权限")
	//	return
	// }

	hdMap, err := c.hdMapService.Find(user.Id)
	if err != nil {
		c.Fail("此用户未设置运营地图")
		return
	}

	c.Success(hdMap)
	return
}

// Create 新增设备
func (c *DeviceController) Create() {
	var (
		err       error
		addDevice dto.AddDevice
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &addDevice)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	if userInfo.Id != 1 {
		c.Fail("权限不足")
		return
	}

	imsi := addDevice.IMSI
	imei := addDevice.IMEI
	name := addDevice.Name
	uid := addDevice.UID
	_type := addDevice.Type
	_real := addDevice.Real
	expireTime := addDevice.ExpireTime
	remoteDriving := addDevice.RemoteDriving
	vinLogin := addDevice.VinLogin
	push := addDevice.Push
	historyDays := addDevice.HistoryDays
	bindRemark := addDevice.BindRemark
	mapId := addDevice.MapId
	exist1 := c.service.CheckImsi(imsi)
	if exist1 {
		c.Fail("该车辆终端编号已被占用")
		return
	}
	exist2 := c.service.CheckImei(imei)
	if exist2 {
		c.Fail("该VIN码已被占用")
		return
	}
	err = c.service.AddDevice(imei, imsi, name, uid, _type, _real, expireTime, remoteDriving, vinLogin, push, historyDays, bindRemark, mapId)
	if err != nil {
		c.Fail("新增失败：" + err.Error())
		return
	}

	// 添加终端归属
	if uid != 0 {
		redis.GetRedis().Do("SADD", fmt.Sprintf("%s%d", common.RedisDeviceOwnerUID, uid), imsi)
	}

	redis.GetRedis().Do("Incr", common.RedisDeviceAuthorizedCnt) // 已授权设备数量自增1

	// 操作日志
	logType := "添加终端"
	if e := c.AddOperationLog(userInfo, logType, 2, "", "新增了设备"+imsi+"("+name+")", imsi); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Delete 删除设备
func (c *DeviceController) Delete() {
	var (
		err    error
		postId dto.PostId
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &postId)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	id := int(postId.Id)
	device, err := c.service.Find(id)
	if err != nil {
		c.Fail("查询失败：" + err.Error())
		return
	}

	err = c.service.Delete(id)
	if err != nil {
		c.Fail("删除失败：" + err.Error())
		return
	}

	redis.GetRedis().Do("Decr", common.RedisDeviceAuthorizedCnt) // 已授权设备数量自减1

	// 删除redis中记录的指定用户所拥有某个设备
	redis.GetRedis().Do("SREM", fmt.Sprintf("%s%d", common.RedisDeviceOwnerUID, device.Uid), device.Imsi)

	err = c.recordSignalService.DeleteDevice(id)
	if err != nil {
		c.Fail("删除心跳记录失败：" + err.Error())
		return
	}

	// 操作日志
	logType := "删除终端"
	if e := c.AddOperationLog(userInfo, logType, 2, "", device.Imsi+"("+device.Name+")被删除", device.Imsi); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Detail 设备详情
func (c *DeviceController) Detail() {
	id, err := c.GetInt("id", 0)
	if err != nil {
		c.Fail("参数错误")
		return
	}
	device, err := c.service.Find(id)
	if err != nil {
		c.Fail("查询失败：" + err.Error())
		return
	}
	nowTs := time.Now().Unix()
	mondayTs := common.GetMondayTs()

	item := c.service.CreateRespDeviceData(mondayTs, nowTs, device)
	c.Success(item)
	return
}

// GetPassword 获取密码
func (c *DeviceController) GetPassword() {
	imsi := c.GetString("imsi")

	password, err := c.service.GetPassword(imsi)
	if err != nil {
		c.Fail("查询失败：" + err.Error())
		return
	}

	key := "edMSQwB864ySnDNy"
	// fmt.Println("原文：", password)

	encryptCode, err := common.ECBEncrypt([]byte(password), []byte(key))
	// fmt.Println("密文：" , encryptCode)
	//
	// decryptCode := AesDecrypt(encryptCode, key)
	// fmt.Println("解密结果：", decryptCode)

	if err != nil {
		c.Fail(err.Error())
		return
	}
	c.Success(encryptCode)
	return
}

// Update 修改设备
func (c *DeviceController) Update() {
	var (
		err          error
		updateDevice dto.UpdateDevice
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &updateDevice)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	id := updateDevice.Id
	name := updateDevice.Name
	uid := updateDevice.Uid
	status := updateDevice.MaintainState
	_type := updateDevice.Type
	_real := updateDevice.Real
	expireTime := updateDevice.ExpireTime
	remoteDriving := updateDevice.RemoteDriving
	vinLogin := updateDevice.VinLogin
	push := updateDevice.Push
	historyDays := updateDevice.HistoryDays
	bindRemark := updateDevice.BindRemark
	mapId := updateDevice.MapId
	targetUid := uid

	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	if userInfo.Id != 1 {
		c.Fail("权限不足")
		return
	}

	isShengJi, isEditable := common.IsShengJiLevel(userInfo.Level), common.IsEditableLevel(userInfo.Level)
	if !(isShengJi || isEditable) {
		c.Fail("权限不足")
		return
	}

	device, err := c.service.Find(id)
	oldData := map[string]interface{}{
		"uid":            device.Uid,
		"name":           device.Name,
		"type":           device.Type,
		"real":           device.Real,
		"maintain_state": device.Status,
		"expire_time":    device.ExpireTime,
		"remote_driving": device.RemoteDriving,
		"vin_login":      device.VinLogin,
		"push":           device.Push,
		"history_days":   device.HistoryDays,
		"bind_remark":    device.BindRemark,
		"map_id":         device.MapId,
	}
	if err != nil {
		c.Fail("查询失败：" + err.Error())
		return
	}

	where := map[string]interface{}{
		"id": id,
	}
	if !isShengJi && isEditable {
		targetUid = int(userInfo.Id)
		where["uid"] = targetUid
	}

	col := map[string]interface{}{
		"uid":            nil,
		"name":           name,
		"type":           _type,
		"real":           _real,
		"maintain_state": status,
		"updated_time":   time.Now().Format("2006-01-02 15:04:05"),
		"expire_time":    expireTime,
		"remote_driving": remoteDriving,
		"vin_login":      vinLogin,
		"push":           push,
		"history_days":   historyDays,
		"bind_remark":    bindRemark,
		"map_id":         mapId,
	}

	if targetUid != 0 {
		col["uid"] = uid
	}

	if device.ExpireTime != expireTime {
		col["send_time"] = 0
		err = c.service.DeleteMessage(id)
		if err != nil {
			c.Fail("删除过期消息失败：" + err.Error())
			return
		}
	}

	err = c.service.Update(where, col)
	if err != nil {
		c.Fail("修改信息失败:" + err.Error())
		return
	}

	if status != 0 { // 正在维护则不升级
		// todo
		// upgrade.RemoveNeededUpgradeDevice(device.IMSI)
	}

	// 修改 IP 池的命名
	if name != device.Name {
		err = c.ipServer.Update(device.Ip, name)
		if err != nil {
			c.Fail("修改ip对应的设备名失败：" + err.Error())
			return
		}
	}

	// 将服务器上面对应的终端的 UID 值修改
	if isShengJi {
		if device.Imei != "" {
			// todo
			// if client.All().Contains(device.IMSI) {
			//	client.Get(device.IMSI).(*vars.Terminal).Prop.UID = dataForm.UID
			// }
		}
		// 修改 Redis 中的终端归属
		if targetUid != device.Uid {
			redis.GetRedis().
				Do("SREM", fmt.Sprintf("%s%d", common.RedisDeviceOwnerUID, device.Uid), device.Imsi)

			if targetUid > 0 {
				redis.GetRedis().
					Do("SADD", fmt.Sprintf("%s%d", common.RedisDeviceOwnerUID, uid), device.Imsi)
			}
		}
	}

	// 操作日志
	logType := "修改终端"
	oldLog, _ := json.Marshal(oldData)
	newLog, _ := json.Marshal(col)
	if e := c.AddOperationLog(userInfo, logType, 2, string(oldLog), string(newLog), device.Imsi); e != nil {
		msg := fmt.Sprintf(AddLogFailed, logType, e)
		c.Fail(msg)
		return
	}

	c.Success(nil)
	return
}

// Analysis 数据大屏
func (c *DeviceController) Analysis() {
	area := c.GetString("area", "")
	level, err := c.GetInt("level", 1)
	deviceId, err := c.GetInt("device_id", 0)
	beginTime, err := c.GetInt("begin_time", 0)
	endTime, err := c.GetInt("end_time", 0)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("权限验证失败：" + err.Error())
		return
	}

	userList, err := c.userServer.Child(userInfo, "")

	var idsStr []string
	var idsInt []int64
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
		idsInt = append(idsInt, v.Id)
	}

	allId := strings.Join(idsStr, ",")

	data, err := c.service.Analysis(area, level, allId, deviceId, beginTime, endTime, idsInt)

	if err != nil {
		c.Fail(err.Error())
		return
	}

	c.Success(data)
	return
}

// GetVin 获取vin码
func (c *DeviceController) GetVin() {
	imsi := c.GetString("imsi")

	vin, err := c.service.GetVin(imsi)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(vin)
	return
}

// GetImsi 获取imsi
func (c *DeviceController) GetImsi() {
	vinCode := c.GetString("vin_code")

	vin, err := c.service.GetImsi(vinCode)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(vin)
	return
}

// UpdateVin 更新vin码
func (c *DeviceController) UpdateVin() {
	var (
		err       error
		updateVin dto.UpdateVin
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &updateVin)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	vinPass, _ := beego.AppConfig.String("vin_pass")

	if !strings.EqualFold(updateVin.Password, vinPass) {
		c.Fail("密码不正确")
		return
	}

	err = c.service.UpdateVin(updateVin.Imsi, updateVin.VinCode)
	if err != nil {
		c.Fail("更新失败：" + err.Error())
		return
	}

	c.Success(nil)
	return
}

// GetPushConfig 获取推流配置
func (c *DeviceController) GetPushConfig() {
	imsi := c.GetString("imsi")

	data, err := c.service.GetPushConfig(imsi)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(data)
	return
}

// UpdatePushConfig 更新推流配置
func (c *DeviceController) UpdatePushConfig() {
	var (
		err    error
		update dto.UpdatePushConfig
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &update)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	err = c.service.UpdatePushConfig(update.Imsi, update.Position, update.Open, update.Type)
	if err != nil {
		c.Fail("修改失败," + err.Error())
		return
	}

	c.Success(nil)
	return
}

// PushLog 推流记录
func (c *DeviceController) PushLog() {
	page, err := c.GetInt("page", 1)
	limit, err := c.GetInt("limit", 10)
	imsi := c.GetString("imsi", "")
	position, err := c.GetInt("position", -1)
	_type, err := c.GetInt("type", -1)
	beginTime, err := c.GetInt("begin_time", 0)
	endTime, err := c.GetInt("end_time", 0)

	userInfo, err := c.CheckToken()

	userList, err := c.userServer.Child(userInfo, "")

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	allId := strings.Join(idsStr, ",")

	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	data, err, count := c.service.PushLog(page, limit, position, _type, beginTime, endTime, allId, imsi)
	if err != nil {
		c.Fail("获取失败：" + err.Error())
		return
	}

	c.Success(map[string]interface{}{
		"item":  data,
		"total": count,
	})

	return
}

// Push 开始推流
func (c *DeviceController) Push() {
	var (
		err  error
		push dto.Push
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &push)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	// password, err := c.service.GetPassword(push.Imsi)
	// if err != nil {
	//	c.Fail("获取密码失败:" + err.Error())
	//	return
	// }

	log.Info("收到密码：", push.Pwd)

	ws := websocket.GetWebSocket()
	loginData := map[string]interface{}{
		"cmd":    common.MsgLogin,
		"name":   push.Imsi + "c",
		"pwd":    push.Pwd,
		"forced": true,
	}

	loginDataByte, _ := json.Marshal(loginData)
	ws.WriteMessage(1, loginDataByte)

	position := ""

	switch push.Position {
	case 1:
		position = "front"
		break
	case 2:
		position = "left"
		break
	case 3:
		position = "right"
		break
	case 4:
		position = "back"
		break
	case 5:
		position = "other"
		break
	case 6:
		position = "leftFront"
		break
	case 7:
		position = "rightFront"
		break
	}

	// go func() {
	//	common.CreateWebrtcChannel(ws, push.Imsi, position)
	// }()

	common.MyHash.HSet(push.Imsi, push.Position, make(chan bool))
	go func() {
		myChannel, _ := common.MyHash.HGet(push.Imsi, push.Position)
		for {
			select {
			case <-myChannel:
				return
			default:
				common.CreateWebrtcChannel(ws, push.Imsi, position)
			}
		}

	}()

	c.Success(common.AudioPort)
	return
}

// Stop 停止推流
func (c *DeviceController) Stop() {
	var (
		err  error
		push dto.Push
	)

	data := c.Ctx.Input.RequestBody
	err = common.Validate(data, &push)

	if err != nil {
		c.Fail("参数错误," + err.Error())
		return
	}

	go func() {
		myChannel, _ := common.MyHash.HGet(push.Imsi, push.Position)
		myChannel <- true

	}()

	common.Del(push.Imsi)

	// cmdStr := "ffmpeg -i output.ivf -i output.ogg -c:v copy -c:a libvorbis output.mp4"
	// //fmt.Println("cmd:", cmdStr)
	// cmd := exec.Command("/bin/bash", "-c", cmdStr)
	// output, err :=
	// cmd.StdoutPipe()
	// if err != nil {
	//	c.Fail("无法获取命令的标准输出管道" + err.Error())
	//	return
	// }
	//
	// // 执行Linux命令
	// if err := cmd.Start(); err != nil {
	//	c.Fail("Linux命令执行失败，请检查命令输入是否有误" + err.Error())
	//	return
	// }
	// // 读取输出
	// prints, err := ioutil.ReadAll(output)
	// if err != nil {
	//	c.Fail("打印异常，请检查")
	//	return
	// }
	// if err := cmd.Wait(); err != nil {
	//	c.Fail("Wait" + err.Error())
	//	return
	// }
	//
	// outs := string(prints)
	// logs.Info(outs)
	//
	// endpoint := beego.AppConfig.String("endpoint")
	// accessKeyId := beego.AppConfig.String("accessKeyId")
	// accessKeySecret := beego.AppConfig.String("accessKeySecret")
	// bucketName := beego.AppConfig.String("bucketName")
	// filePath := "output.mp4"
	// ossFilename := fmt.Sprintf("%s%d", common.RandomString("alpha", 5), time.Now().Unix()) + ".mp4"
	//
	// client, err := oss.New(endpoint, accessKeyId, accessKeySecret)
	// if err != nil {
	//	c.Fail(err.Error())
	//	return
	// }
	//
	// bucket, err := client.Bucket(bucketName)
	// if err != nil {
	//	c.Fail(err.Error())
	//	return
	// }
	//
	// err = bucket.PutObjectFromFile(ossFilename, filePath)
	// if err != nil {
	//	fmt.Println("Error:", err)
	//	return
	// }

	c.Success(nil)
	return
}

// Statistics 获取统计数据
func (c *DeviceController) Statistics() {
	// ---- 获取真实的客户端 IP 地址
	var clientIP string

	// 1. 先尝试获取 X-Real-IP
	clientIP = c.Ctx.Input.Header("X-Real-IP")

	// 2. 如果没有 X-Real-IP，则尝试获取 X-Forwarded-For
	if clientIP == "" {
		xForwardedFor := c.Ctx.Input.Header("X-Forwarded-For")
		if xForwardedFor != "" {
			// X-Forwarded-For 可能包含多个 IP，第一个是真实客户端 IP
			ips := strings.Split(xForwardedFor, ",")
			clientIP = strings.TrimSpace(ips[0])
		}
	}

	// 3. 如果还是没有，则使用 RemoteAddr
	if clientIP == "" {
		clientIP = c.Ctx.Input.IP()
	}

	// 获取 IP 地址对应的地理位置
	ipAddrStr, err := common.GetAddr(clientIP)
	if err != nil {
		// 如果获取地址失败，记录错误但不影响主流程
		log.Error("获取IP地址位置信息失败:", err, "IP:", clientIP)
	}

	// 解析地理位置信息
	var ipInfo dto.IpAddrInfo
	if ipAddrStr != "" {
		parts := strings.Split(ipAddrStr, "|")
		if len(parts) >= 5 {
			ipInfo = dto.IpAddrInfo{
				Country:  parts[0],
				Region:   parts[1],
				Province: parts[2],
				City:     parts[3],
				Isp:      parts[4],
			}
		}
	}
	// ---- 获取真实的客户端 IP 地址

	var query dto.StatisticsQuery
	if err := pkg.UnmarshalQuery(c.GetString, &query); err != nil {
		c.Fail("参数解析失败：" + err.Error())
		return
	}

	// 获取当前登录用户的车队列表
	userInfo, err := c.CheckToken()
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	userList, err := c.userServer.Child(userInfo, "")
	if err != nil {
		c.Fail("获取用户信息失败：" + err.Error())
		return
	}

	// 判断查询的车队是否属于当前登录用户
	found := false
	for _, user := range userList {
		if user.Id == query.UID {
			found = true
			break
		}
	}
	if !found {
		c.Fail("权限不足，您无法查询不属于您的车队数据")
		return
	}

	// 获取查询车队和其子车队数据
	userList, err = c.userServer.ChildUser(query.UID, "")
	if err != nil {
		c.Fail(err.Error())
		return
	}

	var idsStr []string
	for _, v := range userList {
		idsStr = append(idsStr, fmt.Sprintf("%d", v.Id))
	}

	// 设置权限相关字段
	query.AllUID = strings.Join(idsStr, ",")

	// 使用注入的 analysisService 获取统计数据
	data, err := c.analysisService.Statistics(&query)
	if err != nil {
		c.Fail("获取统计数据失败：" + err.Error())
		return
	}

	// 将 ipAddr 添加到 data 中
	if data == nil {
		data = make(map[string]interface{})
	}
	data["ipAddr"] = ipInfo

	c.Success(data)
	return
}
