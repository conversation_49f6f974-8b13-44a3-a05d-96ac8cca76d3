package tests

import (
	"testing"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/stretchr/testify/assert"
	"ccapi/models/pix"
	"ccapi/service"
)

// TestGoViewImageService 测试图片服务
func TestGoViewImageService(t *testing.T) {
	// 初始化测试数据库连接
	// 注意：这里需要根据实际情况配置测试数据库
	
	service := &service.GoViewImageService{}
	
	// 测试数据
	projectID := "test_project_1"
	fileName := "test_image.jpg"
	fileURL := "/api/goview/uploads/images/test_image.jpg"
	createUserID := "test_user_1"
	fileSize := int64(1024)
	tags := []string{"background", "component"}

	t.Run("AddImage", func(t *testing.T) {
		// 测试添加图片
		image, err := service.AddImage(projectID, fileName, fileURL, createUserID, fileSize, tags)
		
		assert.NoError(t, err)
		assert.NotNil(t, image)
		assert.Equal(t, projectID, image.ProjectID)
		assert.Equal(t, fileName, image.FileName)
		assert.Equal(t, fileURL, image.FileURL)
		assert.Equal(t, createUserID, image.CreateUserID)
		assert.Equal(t, fileSize, image.FileSize)
		assert.True(t, image.IsActive) // 背景图片应该是活跃的
		
		// 验证标签
		parsedTags, err := service.ParseTags(image.Tags)
		assert.NoError(t, err)
		assert.Contains(t, parsedTags, "background")
		assert.Contains(t, parsedTags, "component")
	})

	t.Run("GetProjectImages", func(t *testing.T) {
		// 测试获取项目图片列表
		images, err := service.GetProjectImages(projectID, []string{})
		
		assert.NoError(t, err)
		assert.NotEmpty(t, images)
		
		// 测试标签过滤
		backgroundImages, err := service.GetProjectImages(projectID, []string{"background"})
		assert.NoError(t, err)
		assert.NotEmpty(t, backgroundImages)
		
		// 验证过滤结果
		for _, img := range backgroundImages {
			assert.True(t, service.HasTag(img, "background"))
		}
	})

	t.Run("SetActiveImage", func(t *testing.T) {
		// 先获取一个图片
		images, err := service.GetProjectImages(projectID, []string{"background"})
		assert.NoError(t, err)
		assert.NotEmpty(t, images)
		
		imageID := images[0].ID
		
		// 测试设置活跃图片
		err = service.SetActiveImage(projectID, imageID)
		assert.NoError(t, err)
		
		// 验证设置结果
		activeImage, err := service.GetActiveImage(projectID, "background")
		assert.NoError(t, err)
		assert.NotNil(t, activeImage)
		assert.Equal(t, imageID, activeImage.ID)
	})

	t.Run("DeleteImage", func(t *testing.T) {
		// 先添加一个测试图片
		testImage, err := service.AddImage(projectID, "delete_test.jpg", "/test/delete_test.jpg", createUserID, 512, []string{"component"})
		assert.NoError(t, err)
		
		// 测试删除图片
		err = service.DeleteImage(projectID, testImage.ID)
		assert.NoError(t, err)
		
		// 验证删除结果
		images, err := service.GetProjectImages(projectID, []string{})
		assert.NoError(t, err)
		
		// 确认图片已被删除
		found := false
		for _, img := range images {
			if img.ID == testImage.ID {
				found = true
				break
			}
		}
		assert.False(t, found)
	})

	t.Run("ParseTags", func(t *testing.T) {
		// 测试标签解析
		tagsJSON := `["background","component","icon"]`
		tags, err := service.ParseTags(tagsJSON)
		
		assert.NoError(t, err)
		assert.Len(t, tags, 3)
		assert.Contains(t, tags, "background")
		assert.Contains(t, tags, "component")
		assert.Contains(t, tags, "icon")
		
		// 测试空标签
		emptyTags, err := service.ParseTags("")
		assert.NoError(t, err)
		assert.Empty(t, emptyTags)
	})

	t.Run("HasTag", func(t *testing.T) {
		// 创建测试图片
		image := &pix.GoViewProjectImage{
			Tags: `["background","component"]`,
		}
		
		// 测试标签检查
		assert.True(t, service.HasTag(image, "background"))
		assert.True(t, service.HasTag(image, "component"))
		assert.False(t, service.HasTag(image, "icon"))
		assert.False(t, service.HasTag(image, "nonexistent"))
	})
}

// TestImageTagFiltering 测试图片标签过滤功能
func TestImageTagFiltering(t *testing.T) {
	service := &service.GoViewImageService{}
	projectID := "test_project_filter"
	createUserID := "test_user_filter"
	
	// 创建不同标签的测试图片
	testImages := []struct {
		fileName string
		tags     []string
	}{
		{"background1.jpg", []string{"background"}},
		{"background2.jpg", []string{"background"}},
		{"icon1.png", []string{"icon"}},
		{"icon2.png", []string{"icon"}},
		{"component1.jpg", []string{"component"}},
		{"mixed1.jpg", []string{"background", "component"}},
	}
	
	// 添加测试图片
	for _, testImg := range testImages {
		_, err := service.AddImage(projectID, testImg.fileName, "/test/"+testImg.fileName, createUserID, 1024, testImg.tags)
		assert.NoError(t, err)
	}
	
	t.Run("FilterByBackground", func(t *testing.T) {
		images, err := service.GetProjectImages(projectID, []string{"background"})
		assert.NoError(t, err)
		assert.Len(t, images, 3) // background1, background2, mixed1
	})
	
	t.Run("FilterByIcon", func(t *testing.T) {
		images, err := service.GetProjectImages(projectID, []string{"icon"})
		assert.NoError(t, err)
		assert.Len(t, images, 2) // icon1, icon2
	})
	
	t.Run("FilterByComponent", func(t *testing.T) {
		images, err := service.GetProjectImages(projectID, []string{"component"})
		assert.NoError(t, err)
		assert.Len(t, images, 2) // component1, mixed1
	})
	
	t.Run("FilterByAll", func(t *testing.T) {
		images, err := service.GetProjectImages(projectID, []string{"all"})
		assert.NoError(t, err)
		assert.Len(t, images, 6) // 所有图片
	})
	
	t.Run("FilterByMultipleTags", func(t *testing.T) {
		images, err := service.GetProjectImages(projectID, []string{"background", "icon"})
		assert.NoError(t, err)
		assert.Len(t, images, 5) // background1, background2, mixed1, icon1, icon2
	})
}

// BenchmarkImageService 性能测试
func BenchmarkImageService(b *testing.B) {
	service := &service.GoViewImageService{}
	projectID := "benchmark_project"
	createUserID := "benchmark_user"
	
	b.Run("AddImage", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			fileName := fmt.Sprintf("benchmark_%d.jpg", i)
			fileURL := fmt.Sprintf("/test/%s", fileName)
			tags := []string{"benchmark"}
			
			_, err := service.AddImage(projectID, fileName, fileURL, createUserID, 1024, tags)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
	
	b.Run("GetProjectImages", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_, err := service.GetProjectImages(projectID, []string{"benchmark"})
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
