package routers

import (
	"ccapi/auth"
	"ccapi/common"
	"ccapi/models/pix"
	"ccapi/server/redis"
	"errors"
	"fmt"
	"strings"

	"github.com/beego/beego/v2/client/orm"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
)

// 无需鉴权的接口
var noCheckUri = []string{"/api/user/login", "/api/hd_map/all_map", "/api/user/team_list", "/api/device/car_list", "/api/user/detail",
	"/api/user/logout", "/api/user/info", "/api/firmware/download", "/api/device/get_map",
	"/api/firmware/list", "/api/firmware/upgrade_vcu", "/api/device/action/reboot", "/api/device/action/cmd",
	"/api/firmware/log", "/api/firmware/delete_log", "/api/firmware/all_log", "/api/user/child", "/api/hd_map/detail",
	"/api/device/action/autoTask", "/api/user/get_map", "/metrics", "/api/cert/download", "/api/device/get_vin", "/api/device/update_vin",
	"/api/device/get_imsi", "/api/operation/cities", "/api/operation/route_page", "/api/operation/coupon_activity_page",
	"/api/user/get_captcha",        // 获取验证码
	"/api/user/verify_captcha",     // 验证验证码
	"/api/user/v2/login",           // 新版登录
	"/api/user/v2/logout",          // 新版登出
	"/api/user/externalCheckToken", // 校验 token
	"/api/user/v2/token/refresh",   // 校验 token
	// 数据库管理接口（开发和监控用）
	"/api/database/health",         // 数据库健康检查
	"/api/database/status",         // 数据库连接状态
	"/api/database/stats",          // 数据库连接统计
	"/api/database/connect-all",    // 连接所有数据库
	// GoView数据可视化接口
	"/api/goview/project/getData",  // GoView获取项目数据（预览功能，无需认证）
	"/api/goview/project/edit",     // GoView编辑项目
	"/api/goview/project/delete",   // GoView删除项目
	"/api/goview/project/publish",  // GoView发布项目
	"/api/goview/project/upload",   // GoView文件上传
}

var wxUri = []string{"/api/wx/user/mobilelogin", "/api/wx/sms/send", "/api/wx/sms/check", "/api/wx/user/logout", "/api/wx/user/thirdregister",
	"/api/wx/user/decpdewx", "/api/wx/user/addremark", "/api/wx/user/getremark", "/api/wx/user/getorderlist", "/api/wx/route/getorderlist"}

var wxNeedCheckUri = []string{"/api/wx/user/logout", "/api/wx/user/decpdewx", "/api/wx/user/addremark", "/api/wx/user/getremark", "/api/wx/user/getorderlist"}

var AdminMiddleware = func(ctx *context.Context) {
	// 全局CORS处理，优先处理OPTIONS
	if ctx.Input.Method() == "OPTIONS" {
		ctx.Output.Header("Access-Control-Allow-Origin", "*")
		ctx.Output.Header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type,Authorization,Origin,Accept,X-Requested-With,X-Token")
		ctx.Output.Header("Access-Control-Allow-Credentials", "true")
		ctx.Output.SetStatus(200)
		ctx.Output.Body([]byte("ok"))
		return
	}

	// 开发环境下，只有携带特定请求头才能跳过鉴权
	if beego.BConfig.RunMode == "dev" {
		devBypass := ctx.Request.Header.Get("X-Dev-Bypass-Auth")
		// 仅供开发调试，严禁生产环境使用
		if devBypass == "let-me-in" {
			return // 跳过鉴权
		}
		// 未携带特定头，继续正常鉴权
	}

	uri := ctx.Request.RequestURI
	index := strings.Index(uri, "?")
	if index > 0 {
		uri = uri[:index]
	}

	fmt.Println(uri)
	// 检查是否是GoView上传的文件路径，如果是则跳过鉴权
	if strings.HasPrefix(uri, "/api/goview/uploads/") {
		return
	}

	if !common.InArray(uri, noCheckUri) && !common.InArray(uri, wxUri) {
		token := ctx.Request.Header.Get("X-Token")

		// 使用统一的token验证方法
		uid, err := auth.VerifyToken(token)
		if err != nil {
			setCORS(ctx)
			ctx.Output.JSON(map[string]interface{}{
				"code": 401,
				"msg":  "登录已失效, 请重新登录",
				"data": nil,
			}, false, false)
			return
		}

		// 验证权限
		err = CheckRule(int(uid), uri)
		if err != nil {
			setCORS(ctx)
			ctx.Output.JSON(map[string]interface{}{
				"code": 1,
				"msg":  "权限验证失败：" + err.Error(),
				"data": nil,
			}, false, false)
			return
		}
	}

	if common.InArray(uri, wxNeedCheckUri) {
		token := ctx.Request.Header.Get("token")
		_, err := CheckWxToken(token)
		if err != nil {
			setCORS(ctx)
			ctx.Output.JSON(map[string]interface{}{
				"code": 1,
				"msg":  err.Error(),
				"data": nil,
			}, false, false)
			return
		}
	}
}

func init() {
	fmt.Printf("当前模式：%s\n", beego.BConfig.RunMode)
}

// CheckRule 验证权限
func CheckRule(id int, uri string) error {
	var (
		err  error
		re   string
		rule []pix.Rule
	)

	o := orm.NewOrm()

	re = redis.Get(fmt.Sprintf("%s%d", common.RedisUserRule, id))
	if re == "" {
		err = errors.New("请退出后重新登录")
		return err
	}
	if re == "all" {
		return nil
	}
	_, err = o.Raw(fmt.Sprintf("select * from rule where FIND_IN_SET(id, '%s')", re)).QueryRows(&rule)
	if err != nil {
		return err
	}

	flag := false

	for _, v := range rule {
		if uri == "/api"+v.Router {
			flag = true
		}
	}

	if flag {
		return nil
	}

	err = errors.New("权限不足")
	return err
}

// CheckWxToken 验证微信登录
func CheckWxToken(token string) (int, error) {
	var (
		err error
		uid string
	)

	if token == "" {
		err = errors.New("令牌不存在")
		return 0, err
	}

	uid = redis.Get(fmt.Sprintf(common.WxRedisSessionMultiTokenUid, token))
	if uid == "" {
		err = errors.New("登录已失效, 请重新登录")
		return 0, err
	}
	id := common.ChangeStringToInt(uid)
	return id, nil
}

// 在所有ctx.Output.JSON(...)前调用setCORS(ctx)
func setCORS(ctx *context.Context) {
	ctx.Output.Header("Access-Control-Allow-Origin", "*")
	ctx.Output.Header("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
	ctx.Output.Header("Access-Control-Allow-Headers", "Content-Type,Authorization,Origin,Accept,X-Requested-With,X-Token")
	ctx.Output.Header("Access-Control-Allow-Credentials", "true")
}
